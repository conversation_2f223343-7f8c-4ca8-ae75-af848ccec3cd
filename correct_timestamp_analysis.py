#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的时间戳分析脚本
根据文档重新分析时间戳格式，应该对应2025年7月23日20:24:35
"""

import struct
from datetime import datetime, timezone
import os

def swap_16(val):
    return ((val & 0x00ff) << 8) | ((val & 0xff00) >> 8)

def swap_32(val):
    return ((val & 0xff000000) >> 24) | \
           ((val & 0x00ff0000) >> 8) | \
           ((val & 0x0000ff00) << 8) | \
           ((val & 0x000000ff) << 24)

def swap_64(value):
    result = 0
    result |= (value & 0xff00000000000000) >> 56
    result |= (value & 0x00ff000000000000) >> 40
    result |= (value & 0x0000ff0000000000) >> 24
    result |= (value & 0x000000ff00000000) >> 8
    result |= (value & 0x00000000ff000000) << 8
    result |= (value & 0x0000000000ff0000) << 24
    result |= (value & 0x000000000000ff00) << 40
    result |= (value & 0x00000000000000ff) << 56
    return result

def convert_endian_double(value_bytes):
    # 先将bytes转为int64
    val_int = struct.unpack('<Q', value_bytes)[0]
    # 进行字节序转换
    val_int_swapped = swap_64(val_int)
    # 将转换后的int64转回double
    return struct.unpack('!d', struct.pack('!Q', val_int_swapped))[0]

def convert_endian_float(value_bytes):
    # 先将bytes转为int32
    val_int = struct.unpack('<I', value_bytes)[0]
    # 进行字节序转换
    val_int_swapped = swap_32(val_int)
    # 将转换后的int32转回float
    return struct.unpack('!f', struct.pack('!I', val_int_swapped))[0]

def analyze_timestamp_formats(timestamp_value, expected_datetime_str="2025-07-23 20:24:35"):
    """
    分析时间戳格式，找到与期望时间最接近的格式
    """
    print(f"原始时间戳值: {timestamp_value}")
    print(f"期望时间: {expected_datetime_str}")
    print("-" * 60)
    
    # 解析期望时间
    expected_dt = datetime.strptime(expected_datetime_str, "%Y-%m-%d %H:%M:%S")
    expected_timestamp = expected_dt.timestamp()
    print(f"期望时间的Unix时间戳: {expected_timestamp}")
    
    # 尝试各种时间戳格式
    formats = [
        ("直接Unix时间戳", lambda x: x),
        ("毫秒时间戳", lambda x: x / 1000),
        ("微秒时间戳", lambda x: x / 1000000),
        ("纳秒时间戳", lambda x: x / 1000000000),
        ("100纳秒时间戳", lambda x: x / 10000000),
        ("Windows FILETIME", lambda x: (x / 10000000) - 11644473600),
        ("GPS时间(从1980-01-06)", lambda x: x + 315964800),  # GPS epoch
        ("GPS周+秒", lambda x: (x // 604800) * 604800 + (x % 604800) + 315964800),
    ]
    
    best_match = None
    min_diff = float('inf')
    
    for name, converter in formats:
        try:
            converted_timestamp = converter(timestamp_value)
            if converted_timestamp > 0:  # 确保是正数
                dt = datetime.fromtimestamp(converted_timestamp, tz=timezone.utc)
                diff = abs(converted_timestamp - expected_timestamp)
                
                print(f"{name:20}: {dt.strftime('%Y-%m-%d %H:%M:%S.%f UTC')} (差值: {diff:.2f}秒)")
                
                if diff < min_diff:
                    min_diff = diff
                    best_match = (name, converted_timestamp, dt)
        except (ValueError, OSError, OverflowError) as e:
            print(f"{name:20}: 转换失败 - {e}")
    
    if best_match:
        print(f"\n最佳匹配: {best_match[0]}")
        print(f"转换后时间: {best_match[2].strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
        print(f"时间差: {min_diff:.2f}秒")
    
    return best_match

def read_timestamp_from_file(file_path, packet_num=0):
    """
    直接从文件中读取时间戳，尝试不同的位置
    """
    print(f"从文件读取时间戳: {file_path}")
    print(f"数据包编号: {packet_num}")
    print("-" * 60)
    
    try:
        with open(file_path, 'rb') as file:
            # 根据文档，时间戳在DeviceState结构体中
            # 计算基础偏移
            base_offset = 408 + packet_num * 405
            
            # DeviceState结构体的字段顺序（根据文档）:
            # 2字节: DeviceState结构体字节长度
            # 2字节: Temperature
            # 2字节: RFState  
            # 2字节: BBState
            # 8字节: AbsoluteTimeStamp
            # 4字节: Latitude
            # 4字节: Longitude
            
            # 跳过IQS_TriggerInfo部分
            trigger_info_offset = base_offset + 2 + 8 + 2 + 2 + 25*4 + 25*8 + 25*1
            
            # 定位到DeviceState开始
            file.seek(trigger_info_offset)
            device_state_size_bytes = file.read(2)
            device_state_size = swap_16(struct.unpack('<H', device_state_size_bytes)[0])
            print(f"DeviceState结构体大小: {device_state_size}字节")
            
            # 读取Temperature, RFState, BBState (各2字节)
            temp_bytes = file.read(2)
            rf_state_bytes = file.read(2) 
            bb_state_bytes = file.read(2)
            
            # 现在读取AbsoluteTimeStamp (8字节, double, 大端序)
            abs_timestamp_bytes = file.read(8)
            print(f"时间戳原始字节: {abs_timestamp_bytes.hex()}")
            
            # 尝试不同的字节序解释
            print("\n尝试不同的字节序:")
            
            # 1. 大端序double
            timestamp_be = struct.unpack('>d', abs_timestamp_bytes)[0]
            print(f"大端序double: {timestamp_be}")
            
            # 2. 小端序double  
            timestamp_le = struct.unpack('<d', abs_timestamp_bytes)[0]
            print(f"小端序double: {timestamp_le}")
            
            # 3. 使用我们的字节序转换函数
            timestamp_converted = convert_endian_double(abs_timestamp_bytes)
            print(f"转换后double: {timestamp_converted}")
            
            # 分析每种格式
            print("\n=== 大端序double分析 ===")
            analyze_timestamp_formats(timestamp_be)
            
            print("\n=== 小端序double分析 ===") 
            analyze_timestamp_formats(timestamp_le)
            
            print("\n=== 转换后double分析 ===")
            analyze_timestamp_formats(timestamp_converted)
            
            return {
                'big_endian': timestamp_be,
                'little_endian': timestamp_le, 
                'converted': timestamp_converted,
                'raw_bytes': abs_timestamp_bytes
            }
            
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def main():
    """主函数"""
    print("=" * 80)
    print("正确的时间戳分析")
    print("=" * 80)
    
    wav_file_path = "./0053_20250723_202435.part1.iq.wav"
    
    if os.path.exists(wav_file_path):
        # 从文件名提取期望时间
        filename = os.path.basename(wav_file_path)
        if "20250723_202435" in filename:
            expected_time = "2025-07-23 20:24:35"
        else:
            expected_time = "2025-07-23 20:24:35"  # 默认值
        
        result = read_timestamp_from_file(wav_file_path, packet_num=0)
        
        if result:
            print(f"\n" + "=" * 80)
            print("结论:")
            print("=" * 80)
            print("请检查上述分析结果，找到与期望时间最接近的格式")
            print("这将帮助确定正确的时间戳解释方式")
    else:
        print(f"文件不存在: {wav_file_path}")

if __name__ == "__main__":
    main()
