"""
测试时频图时间轴修复效果
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
from integrated_iq_map_analyzer import IntegratedIQMapAnalyzer, find_wav_files

def test_time_axis_fix():
    """测试时间轴修复"""
    print("=== 时频图时间轴测试 ===")
    
    # 查找WAV文件
    wav_files = find_wav_files()
    if not wav_files:
        print("没有找到WAV文件，使用模拟数据测试")
        test_with_synthetic_data()
        return
    
    # 使用第一个WAV文件测试
    wav_file = wav_files[0]
    print(f"测试文件: {wav_file}")
    
    # 创建分析器
    analyzer = IntegratedIQMapAnalyzer(wav_file)
    
    # 提取GNSS信息
    gnss_data = analyzer.extract_gnss_info()
    
    if gnss_data and len(gnss_data) > 0:
        iq_data = gnss_data[0]['IQData']
        print(f"IQ数据长度: {len(iq_data)} 个样本")
        
        # 生成时频图并检查时间轴
        test_stft_time_axis(iq_data, analyzer.sample_rate)
    else:
        print("无法获取IQ数据，使用模拟数据测试")
        test_with_synthetic_data()

def test_stft_time_axis(iq_data, sample_rate):
    """测试STFT时间轴"""
    print(f"\n采样率: {sample_rate/1e6:.1f} MHz")
    
    # 转换为复数信号
    i_data = [iq[0] for iq in iq_data[:1000]]  # 只取前1000个样本
    q_data = [iq[1] for iq in iq_data[:1000]]
    complex_signal = np.array(i_data) + 1j * np.array(q_data)
    
    # 计算STFT
    nperseg = 256
    noverlap = 128
    
    f, t, Zxx = signal.stft(complex_signal, 
                           fs=sample_rate, 
                           window='hann', 
                           nperseg=nperseg, 
                           noverlap=noverlap,
                           return_onesided=False)
    
    print(f"原始时间轴范围: {t[0]*1000:.2f} 到 {t[-1]*1000:.2f} ms")
    
    # 修正时间轴
    t_adjusted = t - t[0]
    t_ms = t_adjusted * 1000
    
    print(f"修正后时间轴范围: {t_ms[0]:.2f} 到 {t_ms[-1]:.2f} ms")
    print(f"频率轴范围: {f[0]/1e6:.2f} 到 {f[-1]/1e6:.2f} MHz")
    
    # 绘制对比图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
    
    # 原始时间轴
    power_db = 20 * np.log10(np.abs(Zxx) + 1e-12)
    im1 = ax1.pcolormesh(t * 1000, f / 1e6, power_db, shading='gouraud', cmap='viridis')
    ax1.set_ylabel('Frequency (MHz)')
    ax1.set_xlabel('Time (ms)')
    ax1.set_title('Original Time Axis (可能有负数)')
    ax1.grid(True, alpha=0.3)
    plt.colorbar(im1, ax=ax1, label='Power (dB)')
    
    # 修正后时间轴
    im2 = ax2.pcolormesh(t_ms, f / 1e6, power_db, shading='gouraud', cmap='viridis')
    ax2.set_ylabel('Frequency (MHz)')
    ax2.set_xlabel('Time (ms)')
    ax2.set_title('Fixed Time Axis (从0开始)')
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(0, t_ms[-1])  # 确保从0开始
    plt.colorbar(im2, ax=ax2, label='Power (dB)')
    
    plt.tight_layout()
    plt.savefig('time_axis_comparison.png', dpi=150, bbox_inches='tight')
    print("对比图已保存为: time_axis_comparison.png")
    plt.show()

def test_with_synthetic_data():
    """使用合成数据测试"""
    print("\n=== 使用合成数据测试 ===")
    
    # 生成测试信号
    fs = 10e6  # 10MHz采样率
    t_signal = np.arange(0, 0.001, 1/fs)  # 1ms的信号
    
    # 创建包含多个频率成分的复数信号
    f1, f2, f3 = 1e6, 2e6, 3e6  # 1MHz, 2MHz, 3MHz
    signal_complex = (np.exp(1j * 2 * np.pi * f1 * t_signal) + 
                     0.5 * np.exp(1j * 2 * np.pi * f2 * t_signal) + 
                     0.3 * np.exp(1j * 2 * np.pi * f3 * t_signal))
    
    # 添加噪声
    noise = 0.1 * (np.random.randn(len(signal_complex)) + 1j * np.random.randn(len(signal_complex)))
    signal_complex += noise
    
    print(f"合成信号: 长度={len(signal_complex)}, 采样率={fs/1e6:.1f}MHz")
    print(f"包含频率成分: {f1/1e6:.1f}MHz, {f2/1e6:.1f}MHz, {f3/1e6:.1f}MHz")
    
    # 计算STFT
    nperseg = 256
    noverlap = 128
    
    f, t, Zxx = signal.stft(signal_complex, 
                           fs=fs, 
                           window='hann', 
                           nperseg=nperseg, 
                           noverlap=noverlap,
                           return_onesided=False)
    
    print(f"STFT结果: 时间点数={len(t)}, 频率点数={len(f)}")
    print(f"原始时间轴: {t[0]*1000:.2f} 到 {t[-1]*1000:.2f} ms")
    
    # 修正时间轴
    t_adjusted = t - t[0]
    t_ms = t_adjusted * 1000
    
    print(f"修正后时间轴: {t_ms[0]:.2f} 到 {t_ms[-1]:.2f} ms")
    
    # 绘制结果
    plt.figure(figsize=(12, 8))
    
    # 时频图
    plt.subplot(2, 2, 1)
    power_db = 20 * np.log10(np.abs(Zxx) + 1e-12)
    plt.pcolormesh(t_ms, f / 1e6, power_db, shading='gouraud', cmap='viridis')
    plt.ylabel('Frequency (MHz)')
    plt.xlabel('Time (ms)')
    plt.title('Spectrogram (Fixed Time Axis)')
    plt.colorbar(label='Power (dB)')
    plt.xlim(0, t_ms[-1])
    
    # 时域信号
    plt.subplot(2, 2, 2)
    plt.plot(t_signal * 1000, np.real(signal_complex), label='Real')
    plt.plot(t_signal * 1000, np.imag(signal_complex), label='Imag')
    plt.xlabel('Time (ms)')
    plt.ylabel('Amplitude')
    plt.title('Time Domain Signal')
    plt.legend()
    plt.grid(True)
    
    # 频谱
    plt.subplot(2, 2, 3)
    fft_freq = np.fft.fftfreq(len(signal_complex), 1/fs)
    fft_signal = np.fft.fft(signal_complex)
    plt.plot(fft_freq / 1e6, 20 * np.log10(np.abs(fft_signal) + 1e-12))
    plt.xlabel('Frequency (MHz)')
    plt.ylabel('Power (dB)')
    plt.title('Frequency Spectrum')
    plt.grid(True)
    plt.xlim(-5, 5)
    
    # 功率随时间变化
    plt.subplot(2, 2, 4)
    power_vs_time = np.mean(np.abs(Zxx)**2, axis=0)
    plt.plot(t_ms, 10 * np.log10(power_vs_time + 1e-12))
    plt.xlabel('Time (ms)')
    plt.ylabel('Average Power (dB)')
    plt.title('Power vs Time')
    plt.grid(True)
    plt.xlim(0, t_ms[-1])
    
    plt.tight_layout()
    plt.savefig('synthetic_signal_analysis.png', dpi=150, bbox_inches='tight')
    print("合成信号分析图已保存为: synthetic_signal_analysis.png")
    plt.show()

def main():
    """主函数"""
    print("🔍 时频图时间轴修复测试")
    print("=" * 50)
    
    # 测试实际WAV文件
    test_time_axis_fix()
    
    print("\n" + "=" * 50)
    
    # 测试合成数据
    test_with_synthetic_data()
    
    print("\n✅ 测试完成！")
    print("生成的文件:")
    print("  - time_axis_comparison.png (时间轴对比)")
    print("  - synthetic_signal_analysis.png (合成信号分析)")

if __name__ == "__main__":
    main()
