#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Example usage of the SWP Mode PlayBack Python implementation
"""

import os
from swp_mode_playback import (
    swp_recording_configuration_info,
    swp_recording_power_spectral_density_info,
    swp_mode_playback
)


def example_read_configuration():
    """Example of reading configuration from a spectrum file"""
    print("Example: Reading Configuration Information")
    print("-" * 50)
    
    # Example file path (modify as needed)
    file_path = "./data/0053_20250805_172344.part1.spectrum"
    
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        print("Please place a valid .spectrum file in the ./data/ directory")
        return
    
    # Read configuration
    status, profile, trace_info, trace_number = swp_recording_configuration_info(file_path)
    
    if status == 0:
        print("Configuration read successfully!")
        print(f"Start Frequency: {profile.StartFreq_Hz / 1e6:.3f} MHz")
        print(f"Stop Frequency: {profile.StopFreq_Hz / 1e6:.3f} MHz")
        print(f"Center Frequency: {profile.CenterFreq_Hz / 1e6:.3f} MHz")
        print(f"Span: {profile.Span_Hz / 1e6:.3f} MHz")
        print(f"Reference Level: {profile.RefLevel_dBm} dBm")
        print(f"RBW: {profile.RBW_Hz} Hz")
        print(f"VBW: {profile.VBW_Hz} Hz")
        print(f"Sweep Time: {profile.SweepTime} s")
        print(f"Trace Points: {profile.TracePoints}")
        print()
        print(f"Full Sweep Trace Points: {trace_info.FullsweepTracePoints}")
        print(f"Partial Sweep Trace Points: {trace_info.PartialsweepTracePoints}")
        print(f"Total Hops: {trace_info.TotalHops}")
        print(f"Trace Number: {trace_number}")
    else:
        print(f"Failed to read configuration. Status: {status}")


def example_read_power_data():
    """Example of reading power spectral density data"""
    print("\nExample: Reading Power Spectral Density Data")
    print("-" * 50)
    
    file_path = "./data/0053_20250805_172344.part1.spectrum"
    
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return
    
    # First get configuration to know data size
    status, profile, trace_info, trace_number = swp_recording_configuration_info(file_path)
    if status != 0:
        print("Failed to read configuration")
        return
    
    # Initialize data containers
    freq_hz = []
    power_spec_dbm = []
    meas_aux_info = []
    
    # Read power data
    status = swp_recording_power_spectral_density_info(file_path, freq_hz, power_spec_dbm, meas_aux_info)
    
    if status == 0:
        print(f"Power data read successfully!")
        print(f"Total data points: {len(freq_hz)}")
        print(f"Frequency range: {min(freq_hz)/1e6:.3f} - {max(freq_hz)/1e6:.3f} MHz")
        print(f"Power range: {min(power_spec_dbm):.1f} - {max(power_spec_dbm):.1f} dBm")
        
        # Show first few data points
        print("\nFirst 5 data points:")
        for i in range(min(5, len(freq_hz))):
            print(f"  {freq_hz[i]/1e6:.6f} MHz: {power_spec_dbm[i]:.2f} dBm")
        
        # Show auxiliary info for first measurement
        if meas_aux_info:
            aux = meas_aux_info[0]
            print(f"\nFirst measurement auxiliary info:")
            print(f"  Max Power: {aux.MaxPower_dBm:.2f} dBm at index {aux.MaxIndex}")
            print(f"  Temperature: {aux.Temperature:.1f} °C")
            print(f"  GPS: {aux.Latitude:.6f}, {aux.Longitude:.6f}")
    else:
        print(f"Failed to read power data. Status: {status}")


def example_full_processing():
    """Example of full processing using the main function"""
    print("\nExample: Full Processing")
    print("-" * 50)
    
    # Run the main processing function
    result = swp_mode_playback()
    
    if result == 0:
        print("Processing completed successfully!")
        
        # Check if output file was created
        output_file = "./data/SWPMode_Data.txt"
        if os.path.exists(output_file):
            print(f"Output file created: {output_file}")
            
            # Show file size and first few lines
            file_size = os.path.getsize(output_file)
            print(f"File size: {file_size} bytes")
            
            with open(output_file, 'r') as f:
                lines = f.readlines()
                print(f"Total lines: {len(lines)}")
                print("First 3 lines:")
                for i, line in enumerate(lines[:3]):
                    parts = line.strip().split('\t')
                    if len(parts) == 2:
                        freq = float(parts[0])
                        power = float(parts[1])
                        print(f"  {i+1}: {freq/1e6:.6f} MHz, {power:.2f} dBm")
    else:
        print(f"Processing failed with result: {result}")


def main():
    """Run all examples"""
    print("SWP Mode PlayBack - Python Implementation Examples")
    print("=" * 60)
    
    # Check if data directory exists
    if not os.path.exists("./data/"):
        print("Creating data directory...")
        os.makedirs("./data/")
    
    # Run examples
    example_read_configuration()
    example_read_power_data()
    example_full_processing()
    
    print("\nExamples completed!")
    print("\nTo use with your own files:")
    print("1. Place your .spectrum files in the ./data/ directory")
    print("2. Update the file_path variable in the functions")
    print("3. Run: python swp_mode_playback.py")


if __name__ == "__main__":
    main()
