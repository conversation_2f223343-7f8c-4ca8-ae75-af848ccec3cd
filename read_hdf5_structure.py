import h5py
import sys

def explore_hdf5(file_path):
    try:
        with h5py.File(file_path, 'r') as f:
            # Print root level keys
            root_keys = list(f.keys())
            print(f"Root level keys: {root_keys}")
            
            # Print structure summary
            print("\nDetailed Structure Summary:")
            
            # Process each root group
            for root_key in root_keys:
                print(f"\n{root_key}:")
                try:
                    root_group = f[root_key]
                    subgroups = list(root_group.keys())
                    print(f"  Contains {len(subgroups)} subgroups")
                    
                    # Count total datasets and points
                    total_datasets = 0
                    total_i_points = 0
                    total_q_points = 0
                    
                    # Process each subgroup
                    for subgroup_name in subgroups:
                        subgroup = root_group[subgroup_name]
                        datasets = list(subgroup.keys())
                        total_datasets += len(datasets)
                        
                        # Process each dataset in the subgroup
                        for dataset_name in datasets:
                            dataset = subgroup[dataset_name]
                            if dataset_name == 'I_data':
                                total_i_points += dataset.size
                            elif dataset_name == 'Q_data':
                                total_q_points += dataset.size
                    
                    # Print summary of datasets
                    print(f"  Total datasets: {total_datasets}")
                    print(f"  Total I_data points: {total_i_points}")
                    print(f"  Total Q_data points: {total_q_points}")
                    print(f"  Total data points: {total_i_points + total_q_points}")
                    
                    # Print example of first subgroup
                    if subgroups:
                        first_subgroup = subgroups[0]
                        print(f"\n  Example - Subgroup '{first_subgroup}':")
                        for dataset_name in root_group[first_subgroup].keys():
                            dataset = root_group[first_subgroup][dataset_name]
                            print(f"    {dataset_name}: shape={dataset.shape}, dtype={dataset.dtype}")
                            # Show sample data (first 5 elements)
                            if dataset.size > 0:
                                sample = dataset[:min(5, dataset.shape[0])]
                                print(f"    {dataset_name} sample data: {sample}")
                    
                    # List all subgroups
                    print(f"\n  All subgroups: {sorted(subgroups, key=lambda x: int(x) if x.isdigit() else int(x.rstrip('0123456789')))}")
                    
                except Exception as e:
                    print(f"  Error processing group {root_key}: {e}")
                
    except Exception as e:
        print(f"Error reading HDF5 file: {e}")

if __name__ == "__main__":
    file_path = "iq_data.h5"
    explore_hdf5(file_path) 