import pandas as pd
import numpy as np
import h5py
import os
import sys
import logging

"""
主要功能：将data文件夹中的CSV文件读取并保存到HDF5文件中
处理流程：
1. 遍历data文件夹中的所有子文件夹（每个子文件夹代表一个信号类别）
2. 读取每个子文件夹中的所有CSV文件（包含I/Q数据）
3. 将所有数据组织并保存到一个HDF5文件中，保持原有的目录结构
4. 打印处理结果摘要
"""

# 配置日志系统，同时输出到文件和控制台
logging.basicConfig(
    level=logging.INFO,  # 设置日志级别为INFO
    format='%(asctime)s - %(levelname)s - %(message)s',  # 设置日志格式：时间-级别-消息
    handlers=[
        logging.FileHandler('iq_data_processing.log'),  # 日志输出到文件
        logging.StreamHandler()  # 日志输出到控制台
    ]
)
logger = logging.getLogger(__name__)  # 获取logger对象

def read_csv_file(file_path):
    """
    读取由main.py生成的CSV文件，提取I_data、Q_data、CenterFreq_Hz和IQSampleRate
    
    CSV文件格式：
    - 前4行为元数据（设备ID、中心频率、采样率、抽取因子）
    - 从第5行开始为I/Q数据（两列：I_Data和Q_Data）
    
    Args:
        file_path (str): CSV文件路径
        
    Returns:
        dict: 包含I_data、Q_data、CenterFreq_Hz和IQSampleRate的字典，读取失败则返回None
    """
    try:
        # 读取头部信息（前4行）
        header_info = {}
        with open(file_path, 'r') as f:
            for i in range(4):
                line = f.readline().strip()
                if i > 0:  # 跳过第一行（设备ID）
                    key, value = line.split(':,')
                    header_info[key] = float(value)  # 将值转换为浮点数
        
        # 读取I_data和Q_data（从第5行开始）
        data = pd.read_csv(file_path, skiprows=4)
        
        # 检查CSV文件是否包含必要的列
        if 'I_Data' not in data.columns or 'Q_Data' not in data.columns:
            logger.error(f"CSV文件 {file_path} 缺少必要的列：I_Data或Q_Data")
            return None
            
        # 检查数据是否为空
        if len(data) == 0:
            logger.error(f"CSV文件 {file_path} 不包含数据")
            return None
        
        return {
            'I_data': data['I_Data'].values,  # 提取I数据并转换为NumPy数组
            'Q_data': data['Q_Data'].values,  # 提取Q数据并转换为NumPy数组
            'CenterFreq_Hz': header_info.get('CenterFreq_Hz', 0),  # 获取中心频率，如果不存在则默认为0
            'IQSampleRate': header_info.get('IQSampleRate', 0)  # 获取采样率，如果不存在则默认为0
        }
    except Exception as e:
        logger.error(f"读取CSV文件 {file_path} 时出错: {e}")
        return None

def save_to_hdf5(file_path, data_dict):
    """
    将多个数据集保存到HDF5文件中
    
    HDF5结构:
    - 根目录
      - 类别1 (组)
        - 文件1 (组)
          - I_data (数据集)
          - Q_data (数据集)
          - 属性: CenterFreq_Hz, IQSampleRate, class
        - 文件2 (组)
          ...
      - 类别2 (组)
        ...
    
    Args:
        file_path (str): 要创建的HDF5文件路径
        data_dict (dict): 包含按类别组织的多个CSV文件数据的字典
    """
    try:
        with h5py.File(file_path, 'w') as f:  # 创建或覆盖HDF5文件
            # 为每个类别创建一个组
            for class_name, files_data in data_dict.items():
                class_group = f.create_group(class_name)  # 创建类别组
                
                # 为类别内的每个文件创建一个组
                for file_name, data in files_data.items():
                    if data is None:  # 跳过无效数据
                        continue
                    
                    file_group = class_group.create_group(file_name)  # 创建文件组
                    
                    # 将I_data和Q_data保存为数据集
                    file_group.create_dataset('I_data', data=data['I_data'])
                    file_group.create_dataset('Q_data', data=data['Q_data'])
                    
                    # 将元数据保存为属性
                    file_group.attrs['CenterFreq_Hz'] = data['CenterFreq_Hz']
                    file_group.attrs['IQSampleRate'] = data['IQSampleRate']
                    
                    # 添加类别属性
                    file_group.attrs['class'] = class_name
    except Exception as e:
        logger.error(f"保存到HDF5文件 {file_path} 时出错: {e}")
        sys.exit(1)  # 出错时退出程序

def print_summary(data_dict):
    """
    打印数据摘要信息
    
    Args:
        data_dict (dict): 包含按类别组织的多个CSV文件数据的字典
    """
    logger.info("已保存数据摘要:")
    total_files = 0  # 处理的总文件数
    
    # 遍历每个类别
    for class_name, files_data in data_dict.items():
        valid_files = {k: v for k, v in files_data.items() if v is not None}  # 过滤有效文件
        logger.info(f"类别: {class_name} - {len(valid_files)} 个文件")
        total_files += len(valid_files)
        
        # 打印每个类别的第一个文件的详细信息作为示例
        if valid_files:
            example_file = next(iter(valid_files.items()))
            file_name, data = example_file
            logger.info(f"  示例文件: {file_name}")
            logger.info(f"    中心频率: {data['CenterFreq_Hz']} Hz")
            logger.info(f"    IQ采样率: {data['IQSampleRate']} Hz")
            logger.info(f"    I数据形状: {data['I_data'].shape}")
            logger.info(f"    Q数据形状: {data['Q_data'].shape}")
    
    logger.info(f"处理的总文件数: {total_files}")

def main():
    """
    主函数：读取data目录中的所有CSV文件并保存到HDF5文件
    
    处理步骤:
    1. 检查data目录是否存在
    2. 遍历data目录中的所有子目录（类别）
    3. 读取每个类别目录中的所有CSV文件
    4. 将所有数据保存到HDF5文件
    5. 打印处理摘要
    """
    # 文件路径
    data_dir = 'data'  # 数据目录
    hdf5_file = 'iq_data.h5'  # 输出的HDF5文件
    
    logger.info(f"开始处理 {data_dir} 中的CSV文件")
    
    # 检查数据目录是否存在
    if not os.path.exists(data_dir):
        logger.error(f"错误: 目录 {data_dir} 不存在")
        sys.exit(1)
    
    # 用于存储按类别组织的所有数据的字典
    data_dict = {}
    
    # 遍历数据目录
    for class_dir in os.listdir(data_dir):
        class_path = os.path.join(data_dir, class_dir)
        
        # 跳过非目录项
        if not os.path.isdir(class_path):
            continue
        
        # 处理包含空格的目录名（如"Single carrier1"）
        class_name = class_dir.replace(' ', '_')  # 将空格替换为下划线，确保HDF5兼容性
        
        logger.info(f"处理类别: {class_dir} (保存为 {class_name})")
        data_dict[class_name] = {}
        
        # 处理此类别目录中的所有CSV文件
        for file_name in os.listdir(class_path):
            if not file_name.endswith('.csv'):
                continue
                
            file_path = os.path.join(class_path, file_name)
            logger.info(f"读取数据: {file_path}...")
            
            # 读取CSV文件数据
            file_data = read_csv_file(file_path)
            
            if file_data:
                # 以文件名（不含扩展名）为键存储数据
                base_name = os.path.splitext(file_name)[0]
                data_dict[class_name][base_name] = file_data
                
                # 记录文件的基本信息
                logger.info(f"  中心频率: {file_data['CenterFreq_Hz']} Hz")
                logger.info(f"  IQ采样率: {file_data['IQSampleRate']} Hz")
                logger.info(f"  样本数: {len(file_data['I_data'])}")
    
    # 将所有数据保存到HDF5文件
    logger.info(f"保存所有数据到 {hdf5_file}...")
    save_to_hdf5(hdf5_file, data_dict)
    logger.info(f"数据已成功保存到 {hdf5_file}")
    
    # 打印保存内容摘要
    print_summary(data_dict)
    
    logger.info("处理完成")

if __name__ == "__main__":
    main()
