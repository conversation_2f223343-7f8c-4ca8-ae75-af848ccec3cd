#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python implementation of SWPMode_PlayBack.cpp
Reads and parses spectrum recording files with msgpack configuration data
"""

import struct
import os
import msgpack
from typing import Tuple, List, Optional
from dataclasses import dataclass
from enum import IntEnum


# Enums for various configuration types (simplified versions)
class SWP_FreqAssignment_TypeDef(IntEnum):
    AUTO = 0
    MANUAL = 1

class Window_TypeDef(IntEnum):
    RECTANGULAR = 0
    HANNING = 1
    HAMMING = 2

class RBWMode_TypeDef(IntEnum):
    AUTO = 0
    MANUAL = 1

class VBWMode_TypeDef(IntEnum):
    AUTO = 0
    MANUAL = 1

class SweepTimeMode_TypeDef(IntEnum):
    AUTO = 0
    MANUAL = 1

class Detector_TypeDef(IntEnum):
    PEAK = 0
    AVERAGE = 1
    RMS = 2

class TraceFormat_TypeDef(IntEnum):
    POWER = 0
    VOLTAGE = 1

class DataFormat_TypeDef(IntEnum):
    FLOAT = 0
    DOUBLE = 1


@dataclass
class SWP_Profile_TypeDef:
    """Configuration profile structure"""
    StartFreq_Hz: float = 0.0
    StopFreq_Hz: float = 0.0
    CenterFreq_Hz: float = 0.0
    Span_Hz: float = 0.0
    RefLevel_dBm: float = 0.0
    RBW_Hz: float = 0.0
    VBW_Hz: float = 0.0
    SweepTime: float = 0.0
    TraceBinSize_Hz: float = 0.0
    FreqAssignment: int = 0
    Window: int = 0
    RBWMode: int = 0
    VBWMode: int = 0
    SweepTimeMode: int = 0
    Detector: int = 0
    TraceFormat: int = 0
    TraceDetectMode: int = 0
    TraceDetector: int = 0
    TracePoints: int = 0
    TracePointsStrategy: int = 0
    TraceAlign: int = 0
    FFTExecutionStrategy: int = 0
    RxPort: int = 0
    SpurRejection: int = 0
    ReferenceClockSource: int = 0
    ReferenceClockFrequency: float = 0.0
    EnableReferenceClockOut: int = 0
    SystemClockSource: int = 0
    ExternalSystemClockFrequency: float = 0.0
    TriggerSource: int = 0
    TriggerEdge: int = 0
    TriggerOutMode: int = 0
    TriggerOutPulsePolarity: int = 0
    PowerBalance: int = 0
    GainStrategy: int = 0
    Preamplifier: int = 0
    AnalogIFBWGrade: int = 0
    IFGainGrade: int = 0
    EnableDebugMode: int = 0
    CalibrationSettings: int = 0
    Atten: int = 0
    TraceType: int = 0
    LOOptimization: int = 0


@dataclass
class SWP_TraceInfo_TypeDef:
    """Trace information structure"""
    FullsweepTracePoints: int = 0
    PartialsweepTracePoints: int = 0
    TotalHops: int = 0
    UserStartIndex: int = 0
    UserStopIndex: int = 0
    TraceBinBW_Hz: float = 0.0
    StartFreq_Hz: float = 0.0
    AnalysisBW_Hz: float = 0.0
    TraceDetectRatio: int = 0
    DecimateFactor: int = 0
    FrameTimeMultiple: float = 0.0
    FrameTime: float = 0.0
    EstimateMinSweepTime: float = 0.0
    DataFormat: int = 0
    SamplePoints: int = 0
    GainParameter: int = 0
    DSPPlatform: int = 0


@dataclass
class MeasAuxInfo_TypeDef:
    """Measurement auxiliary information structure"""
    MaxIndex: int = 0
    MaxPower_dBm: float = 0.0
    Temperature: float = 0.0
    RFState: int = 0
    BBState: int = 0
    GainPattern: int = 0
    ConvertPattern: int = 0
    SysTimeStamp: float = 0.0
    AbsoluteTimeStamp: float = 0.0
    Latitude: float = 0.0
    Longitude: float = 0.0


def swap_bytes_uint64(value: int) -> int:
    """Convert uint64 from big-endian to little-endian"""
    return struct.unpack('<Q', struct.pack('>Q', value))[0]


def swap_bytes_uint32(value: int) -> int:
    """Convert uint32 from big-endian to little-endian"""
    return struct.unpack('<I', struct.pack('>I', value))[0]


def swap_bytes_uint16(value: int) -> int:
    """Convert uint16 from big-endian to little-endian"""
    return struct.unpack('<H', struct.pack('>H', value))[0]


def swap_bytes_int16(value: int) -> int:
    """Convert int16 from big-endian to little-endian"""
    return struct.unpack('<h', struct.pack('>h', value))[0]


def swap_bytes_float(value: float) -> float:
    """Convert float from big-endian to little-endian by swapping bytes"""
    # Pack as big-endian float, unpack as big-endian int, swap bytes, pack as little-endian int, unpack as little-endian float
    packed_be = struct.pack('>f', value)
    int_be = struct.unpack('>I', packed_be)[0]
    int_le = swap_bytes_uint32(int_be)
    packed_le = struct.pack('<I', int_le)
    return struct.unpack('<f', packed_le)[0]


def swap_bytes_double(value: float) -> float:
    """Convert double from big-endian to little-endian by swapping bytes"""
    # Pack as big-endian double, unpack as big-endian int, swap bytes, pack as little-endian int, unpack as little-endian double
    packed_be = struct.pack('>d', value)
    int_be = struct.unpack('>Q', packed_be)[0]
    int_le = swap_bytes_uint64(int_be)
    packed_le = struct.pack('<Q', int_le)
    return struct.unpack('<d', packed_le)[0]


def swp_recording_configuration_info(file_path: str) -> Tuple[int, Optional[SWP_Profile_TypeDef], Optional[SWP_TraceInfo_TypeDef], int]:
    """
    Read configuration information and trace information from recording file
    
    Args:
        file_path: Path to the recording file
        
    Returns:
        Tuple of (status, profile, trace_info, trace_numbers)
    """
    try:
        with open(file_path, 'rb') as file:
            # Read PacketCount at offset 64
            file.seek(64)
            packet_count_bytes = file.read(8)
            packet_count = struct.unpack('>Q', packet_count_bytes)[0]  # Big-endian uint64
            packet_count = swap_bytes_uint64(packet_count)
            
            # Read Length at offset 72 + 10MB
            file.seek(72 + 10 * 1024 * 1024)
            length_bytes = file.read(2)
            length = struct.unpack('>H', length_bytes)[0]  # Big-endian uint16
            length = swap_bytes_uint16(length)
            
            # Read configuration data
            config_data = file.read(length)
            
            if not config_data:
                return -1001, None, None, 0
            
            # Unpack msgpack data
            unpacker = msgpack.Unpacker(raw=False)
            unpacker.feed(config_data)

            profile = SWP_Profile_TypeDef()

            # Unpack profile data in sequence
            try:
                profile.StartFreq_Hz = next(unpacker)
                print(f"Debug: StartFreq_Hz = {profile.StartFreq_Hz}")
                profile.StopFreq_Hz = next(unpacker)
                print(f"Debug: StopFreq_Hz = {profile.StopFreq_Hz}")
                profile.CenterFreq_Hz = next(unpacker)
                print(f"Debug: CenterFreq_Hz = {profile.CenterFreq_Hz}")
            except Exception as e:
                print(f"Error unpacking msgpack data: {e}")
                return -1001, None, None, 0
            profile.Span_Hz = next(unpacker)
            profile.RefLevel_dBm = next(unpacker)
            profile.RBW_Hz = next(unpacker)
            profile.VBW_Hz = next(unpacker)
            profile.SweepTime = next(unpacker)
            profile.TraceBinSize_Hz = next(unpacker)
            profile.FreqAssignment = next(unpacker)
            profile.Window = next(unpacker)
            profile.RBWMode = next(unpacker)
            profile.VBWMode = next(unpacker)
            profile.SweepTimeMode = next(unpacker)
            profile.Detector = next(unpacker)
            profile.TraceFormat = next(unpacker)
            profile.TraceDetectMode = next(unpacker)
            profile.TraceDetector = next(unpacker)
            profile.TracePoints = next(unpacker)
            profile.TracePointsStrategy = next(unpacker)
            profile.TraceAlign = next(unpacker)
            profile.FFTExecutionStrategy = next(unpacker)
            profile.RxPort = next(unpacker)
            profile.SpurRejection = next(unpacker)
            profile.ReferenceClockSource = next(unpacker)
            profile.ReferenceClockFrequency = next(unpacker)
            profile.EnableReferenceClockOut = next(unpacker)
            profile.SystemClockSource = next(unpacker)
            profile.ExternalSystemClockFrequency = next(unpacker)
            profile.TriggerSource = next(unpacker)
            profile.TriggerEdge = next(unpacker)
            profile.TriggerOutMode = next(unpacker)
            profile.TriggerOutPulsePolarity = next(unpacker)
            profile.PowerBalance = next(unpacker)
            profile.GainStrategy = next(unpacker)
            profile.Preamplifier = next(unpacker)
            profile.AnalogIFBWGrade = next(unpacker)
            profile.IFGainGrade = next(unpacker)
            profile.EnableDebugMode = next(unpacker)
            profile.CalibrationSettings = next(unpacker)
            profile.Atten = next(unpacker)
            profile.TraceType = next(unpacker)
            profile.LOOptimization = next(unpacker)

            # C++ code has "off = off * 2" here, which suggests we need to skip some data
            # Let's try to skip the next item to align with the C++ behavior
            try:
                skipped_item = next(unpacker)
                print(f"Debug: Skipped item after LOOptimization: {skipped_item}")
            except StopIteration:
                print("Debug: No item to skip after LOOptimization")

            # Unpack trace info
            trace_info = SWP_TraceInfo_TypeDef()
            trace_info.FullsweepTracePoints = next(unpacker)
            print(f"Debug: FullsweepTracePoints = {trace_info.FullsweepTracePoints}")
            trace_info.PartialsweepTracePoints = next(unpacker)
            trace_info.TotalHops = next(unpacker)
            trace_info.UserStartIndex = next(unpacker)
            trace_info.UserStopIndex = next(unpacker)
            trace_info.TraceBinBW_Hz = next(unpacker)
            trace_info.StartFreq_Hz = next(unpacker)
            trace_info.AnalysisBW_Hz = next(unpacker)
            trace_info.TraceDetectRatio = next(unpacker)
            trace_info.DecimateFactor = next(unpacker)
            trace_info.FrameTimeMultiple = next(unpacker)
            trace_info.FrameTime = next(unpacker)
            trace_info.EstimateMinSweepTime = next(unpacker)
            trace_info.DataFormat = next(unpacker)
            trace_info.SamplePoints = next(unpacker)
            trace_info.GainParameter = next(unpacker)
            trace_info.DSPPlatform = next(unpacker)
            
            # Calculate trace numbers
            if trace_info.PartialsweepTracePoints == 0 or trace_info.FullsweepTracePoints == 0:
                print("Warning: Invalid trace points configuration")
                trace_number1 = 1  # Default to 1 trace
            else:
                points_ratio = trace_info.FullsweepTracePoints // trace_info.PartialsweepTracePoints
                if points_ratio == 0:
                    points_ratio = 1  # Prevent division by zero

                trace_number1 = packet_count // points_ratio
                trace_number2 = packet_count / (trace_info.FullsweepTracePoints / trace_info.PartialsweepTracePoints)

                if trace_number2 > trace_number1:
                    trace_number1 += 1

            return 0, profile, trace_info, trace_number1
            
    except FileNotFoundError:
        print("File opening failed!")
        return -1000, None, None, 0
    except Exception as e:
        print(f"Error reading configuration: {e}")
        return -1001, None, None, 0


def swp_recording_power_spectral_density_info(file_path: str, freq_hz: List[float], power_spec_dbm: List[float], meas_aux_info: List[MeasAuxInfo_TypeDef]) -> int:
    """
    Read frequency array, power array and MeasAuxInfo data from recording file

    Args:
        file_path: Path to the recording file
        freq_hz: List to store frequency data
        power_spec_dbm: List to store power data
        meas_aux_info: List to store measurement auxiliary info

    Returns:
        Status code (0 for success, negative for error)
    """
    try:
        with open(file_path, 'rb') as file:
            # Read API version at offset 8
            file.seek(8)
            api_version_bytes = file.read(4)
            api_version_raw = struct.unpack('<I', api_version_bytes)[0]  # Read as little-endian first

            # Apply the same byte swapping as C++ code
            api_version = ((api_version_raw & 0xFF) << 24) | ((api_version_raw & 0xFF00) << 8) | ((api_version_raw & 0xFF0000) >> 8) | ((api_version_raw & 0xFF000000) >> 24)

            # Extract version components
            x = (api_version & 0xffff0000) >> 16
            y = (api_version & 0x0000ff00) >> 8
            z = api_version & 0x000000ff

            print(f"Debug: API Version raw: 0x{api_version:08X}")
            print(f"Debug: Version components: x={x}, y={y}, z={z}")

            # Check if version is 55
            if y != 55:
                print(f"The file is not version 55 (found version {y}), please select the version 55 file")
                return -1002

            # Read PacketCount at offset 64
            file.seek(64)
            packet_count_bytes = file.read(8)
            packet_count = struct.unpack('>Q', packet_count_bytes)[0]  # Big-endian uint64
            packet_count = swap_bytes_uint64(packet_count)

            # Read Length at offset 72 + 10MB
            file.seek(72 + 10 * 1024 * 1024)
            length_bytes = file.read(2)
            length = struct.unpack('>H', length_bytes)[0]  # Big-endian uint16
            length = swap_bytes_uint16(length)

            # Read all data packets starting from offset 74 + 10MB + Length
            file.seek(74 + 10 * 1024 * 1024 + length)
            meas_aux_info_all = file.read()

            freq_all = []
            power_all = []
            index = 0

            for i in range(packet_count):
                # Check if we have enough data remaining
                if index + 4 > len(meas_aux_info_all):
                    print(f"Error: Not enough data for packet {i}, index={index}, total_length={len(meas_aux_info_all)}")
                    break

                # Read packet length
                count_length_bytes = meas_aux_info_all[index:index+4]
                if len(count_length_bytes) < 4:
                    print(f"Error: Insufficient bytes for count_length at packet {i}")
                    break
                count_length_raw = struct.unpack('<I', count_length_bytes)[0]  # Read as little-endian first
                # Apply the same byte swapping as C++ code
                count_length = ((count_length_raw & 0xff) << 24) | ((count_length_raw & 0xff00) << 8) | ((count_length_raw & 0xff0000) >> 8) | ((count_length_raw & 0xff000000) >> 24)

                # Calculate number of points
                points = (count_length - 56) // 12
                points_float = (count_length - 56) / 12

                if points_float > points:
                    points += 1

                print(f"Debug: Packet {i}, count_length={count_length}, points={points}")

                # Sanity check for points
                if points < 0 or points > 100000:  # Reasonable upper limit
                    print(f"Error: Invalid points calculation: {points}")
                    break

                index += 4

                # Read frequency data
                for j in range(points):
                    if index + 8 > len(meas_aux_info_all):
                        print(f"Error: Not enough data for frequency at packet {i}, point {j}")
                        break
                    freq_bytes = meas_aux_info_all[index:index+8]
                    if len(freq_bytes) < 8:
                        print(f"Error: Insufficient bytes for frequency at packet {i}, point {j}")
                        break
                    freq = struct.unpack('>d', freq_bytes)[0]  # Big-endian double
                    freq_all.append(freq)
                    index += 8

                # Read power data
                for j in range(points):
                    if index + 4 > len(meas_aux_info_all):
                        print(f"Error: Not enough data for power at packet {i}, point {j}")
                        break
                    power_bytes = meas_aux_info_all[index:index+4]
                    if len(power_bytes) < 4:
                        print(f"Error: Insufficient bytes for power at packet {i}, point {j}")
                        break
                    power = struct.unpack('>f', power_bytes)[0]  # Big-endian float
                    power_all.append(power)
                    index += 4

                # Read auxiliary information
                aux_info = MeasAuxInfo_TypeDef()

                # Read HopIndex
                hop_index_bytes = meas_aux_info_all[index:index+4]
                hop_index = struct.unpack('>I', hop_index_bytes)[0]
                index += 4

                # Read FrameIndex
                frame_index_bytes = meas_aux_info_all[index:index+4]
                frame_index = struct.unpack('>I', frame_index_bytes)[0]
                index += 4

                # Read MaxIndex
                max_index_bytes = meas_aux_info_all[index:index+4]
                max_index = struct.unpack('>I', max_index_bytes)[0]
                aux_info.MaxIndex = swap_bytes_uint32(max_index)
                index += 4

                # Read MaxPower
                max_power_bytes = meas_aux_info_all[index:index+4]
                max_power = struct.unpack('>f', max_power_bytes)[0]
                aux_info.MaxPower_dBm = swap_bytes_float(max_power)
                index += 4

                # Read Temperature
                temp_bytes = meas_aux_info_all[index:index+2]
                temp = struct.unpack('>h', temp_bytes)[0]
                aux_info.Temperature = swap_bytes_int16(temp) * 0.01
                index += 2

                # Read RFState
                rf_state_bytes = meas_aux_info_all[index:index+2]
                rf_state = struct.unpack('>H', rf_state_bytes)[0]
                aux_info.RFState = swap_bytes_uint16(rf_state)
                index += 2

                # Read BBState
                bb_state_bytes = meas_aux_info_all[index:index+2]
                bb_state = struct.unpack('>H', bb_state_bytes)[0]
                aux_info.BBState = swap_bytes_uint16(bb_state)
                index += 2

                # Read GainPattern
                gain_pattern_bytes = meas_aux_info_all[index:index+2]
                gain_pattern = struct.unpack('>H', gain_pattern_bytes)[0]
                aux_info.GainPattern = swap_bytes_uint16(gain_pattern)
                index += 2

                # Read ConvertPattern
                convert_pattern_bytes = meas_aux_info_all[index:index+4]
                convert_pattern = struct.unpack('>I', convert_pattern_bytes)[0]
                aux_info.ConvertPattern = swap_bytes_uint32(convert_pattern)
                index += 4

                # Read SysTimeStamp
                sys_time_bytes = meas_aux_info_all[index:index+8]
                sys_time = struct.unpack('>d', sys_time_bytes)[0]
                aux_info.SysTimeStamp = swap_bytes_double(sys_time)
                index += 8

                # Read AbsoluteTimeStamp
                abs_time_bytes = meas_aux_info_all[index:index+8]
                abs_time = struct.unpack('>d', abs_time_bytes)[0]
                aux_info.AbsoluteTimeStamp = swap_bytes_double(abs_time)
                index += 8

                # Read Latitude
                lat_bytes = meas_aux_info_all[index:index+4]
                lat = struct.unpack('>f', lat_bytes)[0]
                aux_info.Latitude = swap_bytes_float(lat)
                index += 4

                # Read Longitude
                lon_bytes = meas_aux_info_all[index:index+4]
                lon = struct.unpack('>f', lon_bytes)[0]
                aux_info.Longitude = swap_bytes_float(lon)
                index += 4

                meas_aux_info.append(aux_info)

            # Copy data to output arrays
            freq_hz.extend(freq_all)
            power_spec_dbm.extend(power_all)

            return 0

    except FileNotFoundError:
        print("The log file failed to open!")
        return -1000
    except Exception as e:
        print(f"Error reading power spectral density info: {e}")
        return -1001


def swp_mode_playback() -> int:
    """
    Main function to read spectrum recording file and output data

    Returns:
        Status code (0 for success)
    """
    # File path - modify as needed
    file_path = "./data/0053_20250805_172344.part1.spectrum"

    # Create data directory if it doesn't exist
    data_dir = "./data/"
    if not os.path.exists(data_dir):
        try:
            os.makedirs(data_dir)
            print("dataFolder address: Htra_RecordingandPlayBack/data")
        except OSError:
            print(f"Failed to create directory: {data_dir}")
            return 0

    # Read configuration information
    status, swp_profile, trace_info, trace_number = swp_recording_configuration_info(file_path)
    if status != 0:
        print("There was a problem reading the file!")
        return 0

    print(f"Configuration read successfully!")
    print(f"Start Frequency: {swp_profile.StartFreq_Hz / 1e6:.3f} MHz")
    print(f"Stop Frequency: {swp_profile.StopFreq_Hz / 1e6:.3f} MHz")
    print(f"Trace Points: {swp_profile.TracePoints}")
    print(f"Full Sweep Trace Points: {trace_info.FullsweepTracePoints}")
    print(f"Partial Sweep Trace Points: {trace_info.PartialsweepTracePoints}")
    print(f"Total Hops: {trace_info.TotalHops}")
    print(f"Trace Number: {trace_number}")

    # Skip power data reading for now to debug configuration
    print("Skipping power data reading for debugging...")
    return 0

    # Initialize data containers
    total_points = trace_info.FullsweepTracePoints * trace_number
    total_hops = trace_info.TotalHops * trace_number

    freq_hz = []
    power_spec_dbm = []
    meas_aux_info = []

    # Read power spectral density information
    status = swp_recording_power_spectral_density_info(file_path, freq_hz, power_spec_dbm, meas_aux_info)
    if status != 0:
        print("Error reading power spectral density data!")
        return 0

    # Write data to file
    output_file = "./data/SWPMode_Data.txt"
    try:
        with open(output_file, 'w') as f:
            for i in range(len(freq_hz)):
                f.write(f"{freq_hz[i]}\t{power_spec_dbm[i]}\n")
        print(f"Data successfully written to {output_file}")
        print(f"Total data points: {len(freq_hz)}")
        print(f"Trace number: {trace_number}")
        print(f"Full sweep trace points: {trace_info.FullsweepTracePoints}")
        print(f"Partial sweep trace points: {trace_info.PartialsweepTracePoints}")
    except Exception as e:
        print(f"Error writing output file: {e}")
        return 0

    return 0


def main():
    """Main entry point"""
    print("SWP Mode PlayBack - Python Implementation")
    print("=" * 50)

    result = swp_mode_playback()
    if result == 0:
        print("Program completed successfully!")
    else:
        print(f"Program failed with status: {result}")


if __name__ == "__main__":
    main()
