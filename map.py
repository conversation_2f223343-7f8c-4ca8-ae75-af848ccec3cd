import requests
from PIL import Image
from io import BytesIO
from WGS84_GCJ02 import wgs84_to_gcj02
from main import extract_gnss_info
import os

# 高德地图API的URL
url = "https://restapi.amap.com/v3/staticmap"

# 替换为你申请的高德API密钥
api_key = "fd2adfc70d86343b1181195c0fc9a0ee"

def get_latest_coordinates(wav_file_path=None):
    """
    从main.py获取最新的经纬度坐标

    Args:
        wav_file_path: WAV文件路径，如果为None则使用默认路径

    Returns:
        tuple: (经度, 纬度)
    """
    # 如果没有指定文件路径，使用默认路径
    if wav_file_path is None:
        wav_file_path = "./0053_20250725_083011.part1.iq.wav"

    # 检查文件是否存在
    if not os.path.exists(wav_file_path):
        print(f"WAV文件不存在: {wav_file_path}")
        # 返回默认坐标
        return 112.99501037597656, 28.229507446289062

    try:
        # 从WAV文件提取GNSS信息
        print(f"正在从 {wav_file_path} 提取GNSS信息...")
        gnss_data = extract_gnss_info(wav_file_path)

        if gnss_data and len(gnss_data) > 0:
            # 使用最后一个数据包的坐标（最新的位置）
            latest_gnss = gnss_data[-1]
            longitude = latest_gnss['Longitude']
            latitude = latest_gnss['Latitude']

            print(f"从WAV文件获取到坐标: 经度={longitude:.6f}, 纬度={latitude:.6f}")
            print(f"时间信息: {latest_gnss['Beijing_Time']}")
            return longitude, latitude
        else:
            print("未能从WAV文件中提取到GNSS信息，使用默认坐标")
            return 112.99501037597656, 28.229507446289062

    except Exception as e:
        print(f"获取坐标时出错: {e}")
        print("使用默认坐标")
        return 112.99501037597656, 28.229507446289062

def generate_map(wav_file_path=None, zoom_level=18, map_size=(1920, 1080), output_filename="map_image.png"):
    """
    生成地图图片

    Args:
        wav_file_path: WAV文件路径
        zoom_level: 地图缩放级别 (1-20)
        map_size: 地图尺寸 (宽度, 高度)
        output_filename: 输出文件名

    Returns:
        bool: 是否成功生成地图
    """
    # 获取GNSS原始坐标（WGS-84）
    gnss_lon, gnss_lat = get_latest_coordinates(wav_file_path)

    # 转换为高德坐标系（GCJ-02）
    center_lon, center_lat = wgs84_to_gcj02(gnss_lon, gnss_lat)

    # 图片尺寸
    width, height = map_size

    # 构建请求参数
    params = {
        "location": f"{center_lon},{center_lat}",
        "zoom": zoom_level,
        "size": f"{width}*{height}",
        "scale": 2,  # 图片质量，2表示高清
        "maptype": "satellite",  # 卫星地图
        "markers": f"mid,0xFF0000,A:{center_lon},{center_lat}",  # 红色标记点
        "key": api_key
    }

    # 显示坐标信息
    print(f"原始坐标 (WGS-84): 经度={gnss_lon:.6f}, 纬度={gnss_lat:.6f}")
    print(f"转换后坐标 (GCJ-02): 经度={center_lon:.6f}, 纬度={center_lat:.6f}")
    print(f"地图缩放级别: {zoom_level}")
    print(f"地图尺寸: {width}x{height}")

    # 发送请求获取图片
    print("正在获取地图图片...")
    response = requests.get(url, params=params)

    # 检查请求是否成功
    if response.status_code == 200:
        # 将图片保存到本地
        with open(output_filename, "wb") as f:
            f.write(response.content)

        print(f"地图图片已保存到: {output_filename}")

        # 显示图片
        try:
            image = Image.open(BytesIO(response.content))
            image.show()
            print("地图图片已在默认图片查看器中打开")
        except Exception as e:
            print(f"无法显示图片: {e}")
            print(f"但图片已成功保存到 {output_filename}")

        return True
    else:
        print(f"获取地图图片失败，HTTP状态码: {response.status_code}")
        if response.text:
            print(f"错误信息: {response.text}")
        return False

def find_wav_files():
    """
    查找当前目录中的所有WAV文件

    Returns:
        list: WAV文件路径列表
    """
    wav_files = []
    for file in os.listdir('.'):
        if file.endswith('.wav'):
            wav_files.append(file)
    return sorted(wav_files)

def interactive_map_generator():
    """
    交互式地图生成器
    """
    print("=== 基于GNSS数据的交互式地图生成器 ===")

    # 查找WAV文件
    wav_files = find_wav_files()

    if not wav_files:
        print("当前目录中没有找到WAV文件，使用默认坐标生成地图")
        success = generate_map()
        return success

    print(f"找到 {len(wav_files)} 个WAV文件:")
    for i, file in enumerate(wav_files, 1):
        print(f"  {i}. {file}")

    # 让用户选择文件
    try:
        choice = input(f"\n请选择WAV文件 (1-{len(wav_files)}) 或按回车使用最新文件: ").strip()

        if choice == "":
            # 使用最新的文件（按文件名排序的最后一个）
            selected_file = wav_files[-1]
            print(f"使用最新文件: {selected_file}")
        else:
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(wav_files):
                selected_file = wav_files[choice_idx]
                print(f"选择文件: {selected_file}")
            else:
                print("无效选择，使用最新文件")
                selected_file = wav_files[-1]
    except (ValueError, KeyboardInterrupt):
        print("使用最新文件")
        selected_file = wav_files[-1]

    # 询问地图参数
    try:
        zoom = input("请输入缩放级别 (1-20, 默认18): ").strip()
        zoom_level = int(zoom) if zoom else 18
        zoom_level = max(1, min(20, zoom_level))  # 限制在1-20范围内
    except ValueError:
        zoom_level = 18

    # 生成地图
    output_filename = f"map_{os.path.splitext(selected_file)[0]}.png"
    success = generate_map(
        wav_file_path=selected_file,
        zoom_level=zoom_level,
        output_filename=output_filename
    )

    return success

def main():
    """
    主函数 - 演示如何使用地图生成功能
    """
    import sys

    if len(sys.argv) > 1:
        # 命令行模式
        wav_file = sys.argv[1]
        zoom_level = int(sys.argv[2]) if len(sys.argv) > 2 else 18
        output_file = sys.argv[3] if len(sys.argv) > 3 else f"map_{os.path.splitext(os.path.basename(wav_file))[0]}.png"

        print(f"命令行模式: 文件={wav_file}, 缩放={zoom_level}, 输出={output_file}")
        success = generate_map(wav_file, zoom_level, output_filename=output_file)
    else:
        # 交互模式
        success = interactive_map_generator()

    if success:
        print("\n地图生成成功！")
    else:
        print("\n地图生成失败！")

if __name__ == "__main__":
    main()
