"""
演示不同的时频图布局选项
"""

import os
from integrated_iq_map_analyzer import IntegratedIQMapAnalyzer, find_wav_files
from PIL import Image, ImageDraw, ImageFont

def create_layout_comparison():
    """创建不同布局的对比图"""
    print("=== 时频图布局演示 ===")
    
    wav_files = find_wav_files()
    if not wav_files:
        print("没有找到WAV文件")
        return
    
    # 使用最新文件
    wav_file = wav_files[-1]
    print(f"使用文件: {wav_file}")
    
    # 创建分析器
    analyzer = IntegratedIQMapAnalyzer(wav_file)
    
    # 生成当前的布局（时频图在坐标点旁边）
    print("生成新布局: 时频图在坐标点旁边")
    success = analyzer.generate_map_with_spectrogram("layout_beside_marker.png")
    
    if success:
        print("✅ 新布局生成成功")
    else:
        print("❌ 新布局生成失败")

def create_layout_options_demo():
    """演示多种布局选项"""
    print("\n=== 多种布局选项演示 ===")
    
    # 创建一个演示图像，展示不同的布局概念
    demo_width = 1200
    demo_height = 800
    demo_image = Image.new('RGB', (demo_width, demo_height), 'white')
    draw = ImageDraw.Draw(demo_image)
    
    try:
        title_font = ImageFont.truetype("arial.ttf", 24)
        text_font = ImageFont.truetype("arial.ttf", 16)
        small_font = ImageFont.truetype("arial.ttf", 12)
    except:
        title_font = ImageFont.load_default()
        text_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # 标题
    draw.text((demo_width//2 - 150, 20), "时频图布局选项", fill='black', font=title_font)
    
    # 布局1: 右上角（原始布局）
    layout1_x, layout1_y = 50, 80
    map1_w, map1_h = 300, 200
    spec1_w, spec1_h = 150, 100
    
    # 绘制地图框
    draw.rectangle([layout1_x, layout1_y, layout1_x + map1_w, layout1_y + map1_h], 
                   outline='blue', fill='lightblue', width=2)
    draw.text((layout1_x + 10, layout1_y + 10), "地图", fill='black', font=text_font)
    
    # 绘制坐标点
    marker_x = layout1_x + map1_w // 2
    marker_y = layout1_y + map1_h // 2
    draw.ellipse([marker_x - 5, marker_y - 5, marker_x + 5, marker_y + 5], 
                fill='red', outline='black')
    
    # 绘制时频图（右上角）
    spec1_x = layout1_x + map1_w - spec1_w - 10
    spec1_y = layout1_y + 10
    draw.rectangle([spec1_x, spec1_y, spec1_x + spec1_w, spec1_y + spec1_h], 
                   outline='green', fill='lightgreen', width=2)
    draw.text((spec1_x + 10, spec1_y + 10), "时频图", fill='black', font=small_font)
    
    draw.text((layout1_x, layout1_y + map1_h + 10), "布局1: 右上角（原始）", fill='black', font=text_font)
    
    # 布局2: 坐标点旁边（新布局）
    layout2_x, layout2_y = 450, 80
    
    # 绘制地图框
    draw.rectangle([layout2_x, layout2_y, layout2_x + map1_w, layout2_y + map1_h], 
                   outline='blue', fill='lightblue', width=2)
    draw.text((layout2_x + 10, layout2_y + 10), "地图", fill='black', font=text_font)
    
    # 绘制坐标点
    marker_x = layout2_x + map1_w // 2
    marker_y = layout2_y + map1_h // 2
    draw.ellipse([marker_x - 5, marker_y - 5, marker_x + 5, marker_y + 5], 
                fill='red', outline='black')
    
    # 绘制时频图（坐标点右侧）
    spec2_x = layout2_x + map1_w + 20
    spec2_y = marker_y - spec1_h // 2
    draw.rectangle([spec2_x, spec2_y, spec2_x + spec1_w, spec2_y + spec1_h], 
                   outline='green', fill='lightgreen', width=2)
    draw.text((spec2_x + 10, spec2_y + 10), "时频图", fill='black', font=small_font)
    
    # 绘制连接线
    draw.line([(marker_x, marker_y), (spec2_x, spec2_y + spec1_h // 2)], 
             fill='red', width=2)
    
    draw.text((layout2_x, layout2_y + map1_h + 10), "布局2: 坐标点旁边（新）", fill='black', font=text_font)
    
    # 布局3: 下方居中
    layout3_x, layout3_y = 50, 350
    
    # 绘制地图框
    draw.rectangle([layout3_x, layout3_y, layout3_x + map1_w, layout3_y + map1_h], 
                   outline='blue', fill='lightblue', width=2)
    draw.text((layout3_x + 10, layout3_y + 10), "地图", fill='black', font=text_font)
    
    # 绘制坐标点
    marker_x = layout3_x + map1_w // 2
    marker_y = layout3_y + map1_h // 2
    draw.ellipse([marker_x - 5, marker_y - 5, marker_x + 5, marker_y + 5], 
                fill='red', outline='black')
    
    # 绘制时频图（下方居中）
    spec3_x = layout3_x + (map1_w - spec1_w) // 2
    spec3_y = layout3_y + map1_h + 20
    draw.rectangle([spec3_x, spec3_y, spec3_x + spec1_w, spec3_y + spec1_h], 
                   outline='green', fill='lightgreen', width=2)
    draw.text((spec3_x + 10, spec3_y + 10), "时频图", fill='black', font=small_font)
    
    # 绘制连接线
    draw.line([(marker_x, marker_y), (spec3_x + spec1_w // 2, spec3_y)], 
             fill='red', width=2)
    
    draw.text((layout3_x, spec3_y + spec1_h + 10), "布局3: 下方居中", fill='black', font=text_font)
    
    # 布局4: 左侧
    layout4_x, layout4_y = 450, 350
    
    # 绘制时频图（左侧）
    spec4_x = layout4_x
    spec4_y = layout4_y + (map1_h - spec1_h) // 2
    draw.rectangle([spec4_x, spec4_y, spec4_x + spec1_w, spec4_y + spec1_h], 
                   outline='green', fill='lightgreen', width=2)
    draw.text((spec4_x + 10, spec4_y + 10), "时频图", fill='black', font=small_font)
    
    # 绘制地图框
    map4_x = layout4_x + spec1_w + 20
    draw.rectangle([map4_x, layout4_y, map4_x + map1_w, layout4_y + map1_h], 
                   outline='blue', fill='lightblue', width=2)
    draw.text((map4_x + 10, layout4_y + 10), "地图", fill='black', font=text_font)
    
    # 绘制坐标点
    marker_x = map4_x + map1_w // 2
    marker_y = layout4_y + map1_h // 2
    draw.ellipse([marker_x - 5, marker_y - 5, marker_x + 5, marker_y + 5], 
                fill='red', outline='black')
    
    # 绘制连接线
    draw.line([(marker_x, marker_y), (spec4_x + spec1_w, spec4_y + spec1_h // 2)], 
             fill='red', width=2)
    
    draw.text((layout4_x, layout4_y + map1_h + 10), "布局4: 左侧", fill='black', font=text_font)
    
    # 添加说明
    explanation_y = 650
    draw.text((50, explanation_y), "当前实现: 布局2 - 时频图显示在坐标点旁边", fill='darkgreen', font=text_font)
    draw.text((50, explanation_y + 25), "优势: 直观显示坐标点与时频图的关联关系", fill='darkgreen', font=text_font)
    draw.text((50, explanation_y + 50), "特点: 连接线、多层标记、装饰边框", fill='darkgreen', font=text_font)
    
    # 保存演示图
    demo_image.save("spectrogram_layout_options.png")
    print("✅ 布局选项演示图已保存: spectrogram_layout_options.png")

def compare_before_after():
    """对比修改前后的效果"""
    print("\n=== 修改前后对比 ===")
    
    print("修改前的特点:")
    print("  - 时频图固定在右上角")
    print("  - 与坐标点位置无关")
    print("  - 简单的矩形布局")
    
    print("\n修改后的特点:")
    print("  - 时频图显示在坐标点旁边")
    print("  - 红色连接线指示关联关系")
    print("  - 多层圆圈标记坐标点")
    print("  - 装饰性边框和标题")
    print("  - 动态布局适应不同地图尺寸")
    
    print("\n视觉改进:")
    print("  ✅ 更直观的空间关联")
    print("  ✅ 更明显的坐标点标记")
    print("  ✅ 专业的图表外观")
    print("  ✅ 清晰的信息层次")

def main():
    """主函数"""
    print("🎨 时频图布局演示")
    print("=" * 50)
    
    # 创建当前布局的示例
    create_layout_comparison()
    
    # 创建布局选项演示
    create_layout_options_demo()
    
    # 对比说明
    compare_before_after()
    
    print("\n" + "=" * 50)
    print("📁 生成的文件:")
    print("  - layout_beside_marker.png (当前布局示例)")
    print("  - spectrogram_layout_options.png (布局选项对比)")
    print("  - test_enhanced_spectrogram_layout.png (增强版布局)")
    
    print("\n🎯 总结:")
    print("新的布局将时频图放置在坐标点旁边，通过连接线和")
    print("视觉元素清晰地表达了地理位置与信号特征的关联关系。")

if __name__ == "__main__":
    main()
