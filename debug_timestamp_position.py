#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试时间戳位置脚本
检查时间戳在文件中的确切位置
"""

import struct
from datetime import datetime, timezone, timedelta

def swap_16(val):
    return ((val & 0x00ff) << 8) | ((val & 0xff00) >> 8)

def swap_32(val):
    return ((val & 0xff000000) >> 24) | \
           ((val & 0x00ff0000) >> 8) | \
           ((val & 0x0000ff00) << 8) | \
           ((val & 0x000000ff) << 24)

def swap_64(value):
    result = 0
    result |= (value & 0xff00000000000000) >> 56
    result |= (value & 0x00ff000000000000) >> 40
    result |= (value & 0x0000ff0000000000) >> 24
    result |= (value & 0x000000ff00000000) >> 8
    result |= (value & 0x00000000ff000000) << 8
    result |= (value & 0x0000000000ff0000) << 24
    result |= (value & 0x000000000000ff00) << 40
    result |= (value & 0x00000000000000ff) << 56
    return result

def convert_endian_double(value_bytes):
    val_int = struct.unpack('<Q', value_bytes)[0]
    val_int_swapped = swap_64(val_int)
    return struct.unpack('!d', struct.pack('!Q', val_int_swapped))[0]

def test_timestamp_at_different_positions(file_path, packet_num=0):
    """
    在不同位置测试读取时间戳
    """
    print(f"测试文件: {file_path}")
    print(f"数据包编号: {packet_num}")
    print("-" * 80)
    
    target_time = "2025-07-23 20:24:35"
    target_dt = datetime.strptime(target_time, "%Y-%m-%d %H:%M:%S")
    target_utc = target_dt - timedelta(hours=8)  # 假设是北京时间，转为UTC
    target_timestamp = target_utc.timestamp()
    
    print(f"目标时间: {target_time} (北京时间)")
    print(f"目标UTC: {target_utc.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"目标Unix时间戳: {target_timestamp}")
    print()
    
    try:
        with open(file_path, 'rb') as file:
            # 根据文档计算基础偏移
            base_offset = 408 + packet_num * 405
            
            # 跳过IQS_TriggerInfo部分到DeviceState
            trigger_info_size = 2 + 8 + 2 + 2 + 25*4 + 25*8 + 25*1
            device_state_start = base_offset + trigger_info_size
            
            print(f"基础偏移: {base_offset}")
            print(f"DeviceState开始位置: {device_state_start}")
            
            # 定位到DeviceState
            file.seek(device_state_start)
            device_state_size_bytes = file.read(2)
            device_state_size = swap_16(struct.unpack('<H', device_state_size_bytes)[0])
            print(f"DeviceState大小: {device_state_size}字节")
            
            # 从DeviceState开始，尝试在不同位置读取8字节作为时间戳
            current_pos = file.tell()
            print(f"当前位置: {current_pos}")
            
            # 测试从当前位置开始的多个8字节位置
            test_positions = []
            for offset in range(0, min(device_state_size, 100), 2):  # 每2字节测试一次
                test_positions.append(current_pos + offset)
            
            print(f"\n测试不同位置的8字节数据作为时间戳:")
            print("-" * 100)
            print(f"{'位置':<8} {'原始字节':<20} {'大端double':<20} {'转换后时间':<30} {'与目标差异(天)':<15}")
            print("-" * 100)
            
            best_matches = []
            
            for pos in test_positions:
                try:
                    file.seek(pos)
                    timestamp_bytes = file.read(8)
                    
                    if len(timestamp_bytes) == 8:
                        # 尝试大端序double
                        timestamp_be = struct.unpack('>d', timestamp_bytes)[0]
                        
                        # 尝试转换为时间
                        for unit_name, divisor in [("微秒", 1000000), ("纳秒", 1000000000), ("直接", 1)]:
                            try:
                                if divisor == 1:
                                    unix_ts = timestamp_be
                                else:
                                    unix_ts = timestamp_be / divisor
                                
                                if unix_ts > 0 and unix_ts < 2e9:  # 合理的Unix时间戳范围
                                    dt_utc = datetime.fromtimestamp(unix_ts, tz=timezone.utc)
                                    dt_beijing = dt_utc + timedelta(hours=8)
                                    
                                    diff_seconds = abs(unix_ts - target_timestamp)
                                    diff_days = diff_seconds / 86400
                                    
                                    if diff_days < 365:  # 差异小于1年
                                        match = {
                                            'position': pos,
                                            'bytes': timestamp_bytes.hex(),
                                            'value': timestamp_be,
                                            'unit': unit_name,
                                            'utc_time': dt_utc,
                                            'beijing_time': dt_beijing,
                                            'diff_days': diff_days
                                        }
                                        best_matches.append(match)
                                        
                                        print(f"{pos:<8} {timestamp_bytes.hex():<20} {timestamp_be:<20.0f} "
                                              f"{dt_beijing.strftime('%Y-%m-%d %H:%M:%S')} ({unit_name}){'':<5} {diff_days:<15.2f}")
                            except:
                                continue
                        
                        # 也尝试我们的字节序转换函数
                        try:
                            timestamp_converted = convert_endian_double(timestamp_bytes)
                            for unit_name, divisor in [("微秒", 1000000), ("纳秒", 1000000000)]:
                                try:
                                    unix_ts = timestamp_converted / divisor
                                    if unix_ts > 0 and unix_ts < 2e9:
                                        dt_utc = datetime.fromtimestamp(unix_ts, tz=timezone.utc)
                                        dt_beijing = dt_utc + timedelta(hours=8)
                                        
                                        diff_seconds = abs(unix_ts - target_timestamp)
                                        diff_days = diff_seconds / 86400
                                        
                                        if diff_days < 365:
                                            match = {
                                                'position': pos,
                                                'bytes': timestamp_bytes.hex(),
                                                'value': timestamp_converted,
                                                'unit': f"转换+{unit_name}",
                                                'utc_time': dt_utc,
                                                'beijing_time': dt_beijing,
                                                'diff_days': diff_days
                                            }
                                            best_matches.append(match)
                                            
                                            print(f"{pos:<8} {timestamp_bytes.hex():<20} {timestamp_converted:<20.0f} "
                                                  f"{dt_beijing.strftime('%Y-%m-%d %H:%M:%S')} (转换+{unit_name}){'':<5} {diff_days:<15.2f}")
                                except:
                                    continue
                        except:
                            pass
                            
                except Exception as e:
                    continue
            
            # 显示最佳匹配
            if best_matches:
                best_matches.sort(key=lambda x: x['diff_days'])
                
                print(f"\n最佳匹配结果:")
                print("-" * 80)
                for i, match in enumerate(best_matches[:3]):
                    print(f"{i+1}. 位置 {match['position']}, 格式: {match['unit']}")
                    print(f"   原始值: {match['value']}")
                    print(f"   UTC时间: {match['utc_time'].strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
                    print(f"   北京时间: {match['beijing_time'].strftime('%Y-%m-%d %H:%M:%S.%f CST')}")
                    print(f"   与目标差异: {match['diff_days']:.4f}天")
                    print()
                
                if best_matches[0]['diff_days'] < 1:
                    print("*** 找到了非常接近的匹配! ***")
                    return best_matches[0]
            
    except Exception as e:
        print(f"读取文件时出错: {e}")
    
    return None

def main():
    """主函数"""
    print("=" * 80)
    print("调试时间戳位置")
    print("=" * 80)
    
    wav_file_path = "./0053_20250723_202435.part1.iq.wav"
    
    result = test_timestamp_at_different_positions(wav_file_path)
    
    if result:
        print("=" * 80)
        print("找到正确的时间戳位置!")
        print("=" * 80)
        print(f"文件位置: {result['position']}")
        print(f"时间戳格式: {result['unit']}")
        print(f"北京时间: {result['beijing_time'].strftime('%Y-%m-%d %H:%M:%S.%f CST')}")

if __name__ == "__main__":
    main()
