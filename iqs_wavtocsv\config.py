#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IQS WAV转换器配置文件
"""

# =============================================================================
# 主要配置参数
# =============================================================================

# 输入目录配置
INPUT_ROOT_DIR = r"D:\nxm-60\Windows\SAStudio4\data"  # 根目录路径，包含所有子文件夹的WAV文件

# 输出目录配置
OUTPUT_BASE_DIR = None  # 输出基础目录，None表示在原文件夹下创建data子文件夹
                       # 例如: r"D:\converted_data" 会将所有转换后的文件放到这个目录下

# 文件过滤配置
WAV_FILE_PATTERN = "*.wav"  # WAV文件匹配模式
INCLUDE_SUBDIRS = True      # 是否包含子目录

# =============================================================================
# 高级配置参数
# =============================================================================

# 日志配置
LOG_LEVEL = "INFO"  # 日志级别: DEBUG, INFO, WARNING, ERROR
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"

# 处理配置
BATCH_SIZE = 1          # 批处理大小（暂未使用）
MAX_WORKERS = 1         # 最大工作线程数（暂未使用）
CONTINUE_ON_ERROR = True  # 遇到错误时是否继续处理其他文件

# 输出配置
CSV_ENCODING = "utf-8"     # CSV文件编码
CSV_SEPARATOR = ","        # CSV分隔符
ADD_TIMESTAMP = False      # 是否在输出文件名中添加时间戳

# 内存配置
MAX_MEMORY_USAGE = 1024    # 最大内存使用量(MB)，暂未使用

# =============================================================================
# 数据格式配置
# =============================================================================

# IQ数据格式映射
DATA_FORMAT_MAP = {
    0: 4,  # Complex32bit
    1: 2,  # Complex16bit  
    2: 1   # Complex8bit
}

# 文件结构常量
STRUCT_SIZE_OFFSET = 108           # 结构体大小偏移量
DEVICE_INFO_OFFSET = None          # 设备信息偏移量（动态计算）
IQ_DATA_OFFSET = 25 * 1024 * 1024 + 408  # IQ数据偏移量
PACKET_SIZE = 64968                # 每个数据包大小

# =============================================================================
# 验证配置
# =============================================================================

def validate_config():
    """验证配置参数"""
    import os
    
    errors = []
    
    # 检查输入目录
    if not os.path.exists(INPUT_ROOT_DIR):
        errors.append(f"输入目录不存在: {INPUT_ROOT_DIR}")
    
    # 检查输出目录
    if OUTPUT_BASE_DIR and not os.path.exists(os.path.dirname(OUTPUT_BASE_DIR)):
        errors.append(f"输出目录的父目录不存在: {os.path.dirname(OUTPUT_BASE_DIR)}")
    
    # 检查日志级别
    valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    if LOG_LEVEL not in valid_log_levels:
        errors.append(f"无效的日志级别: {LOG_LEVEL}，有效值: {valid_log_levels}")
    
    return errors

# =============================================================================
# 配置加载函数
# =============================================================================

def load_config():
    """加载配置并返回配置字典"""
    config = {
        # 路径配置
        'input_root_dir': INPUT_ROOT_DIR,
        'output_base_dir': OUTPUT_BASE_DIR,
        
        # 文件配置
        'wav_file_pattern': WAV_FILE_PATTERN,
        'include_subdirs': INCLUDE_SUBDIRS,
        
        # 日志配置
        'log_level': LOG_LEVEL,
        'log_format': LOG_FORMAT,
        
        # 处理配置
        'batch_size': BATCH_SIZE,
        'max_workers': MAX_WORKERS,
        'continue_on_error': CONTINUE_ON_ERROR,
        
        # 输出配置
        'csv_encoding': CSV_ENCODING,
        'csv_separator': CSV_SEPARATOR,
        'add_timestamp': ADD_TIMESTAMP,
        
        # 数据格式配置
        'data_format_map': DATA_FORMAT_MAP,
        'struct_size_offset': STRUCT_SIZE_OFFSET,
        'iq_data_offset': IQ_DATA_OFFSET,
        'packet_size': PACKET_SIZE,
    }
    
    return config

def print_config():
    """打印当前配置"""
    print("当前配置:")
    print("=" * 50)
    print(f"输入目录: {INPUT_ROOT_DIR}")
    print(f"输出目录: {OUTPUT_BASE_DIR or '(在原文件夹下创建data子文件夹)'}")
    print(f"WAV文件模式: {WAV_FILE_PATTERN}")
    print(f"包含子目录: {INCLUDE_SUBDIRS}")
    print(f"日志级别: {LOG_LEVEL}")
    print(f"遇到错误时继续: {CONTINUE_ON_ERROR}")
    print(f"CSV编码: {CSV_ENCODING}")
    print("=" * 50)

if __name__ == "__main__":
    # 验证配置
    errors = validate_config()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  ✗ {error}")
    else:
        print("✓ 配置验证通过")
    
    # 打印配置
    print_config()
