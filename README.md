# SWP Mode PlayBack - Python Implementation

This is a Python implementation of the C++ SWPMode_PlayBack.cpp program. It reads and parses spectrum recording files with msgpack configuration data.

## Features

- Reads spectrum recording files (.spectrum format)
- Parses msgpack-encoded configuration data
- Handles big-endian to little-endian byte order conversion
- Extracts frequency and power spectral density data
- Outputs data to text file for analysis

## Requirements

- Python 3.6+
- msgpack library

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

1. Place your spectrum recording file in the `./data/` directory
2. Update the file path in the `swp_mode_playback()` function if needed:
```python
file_path = "./data/your_file_name.part1.spectrum"
```
3. Run the program:
```bash
python swp_mode_playback.py
```

## Output

The program will create:
- `./data/SWPMode_Data.txt` - Contains frequency and power data in tab-separated format

## File Structure

- `swp_mode_playback.py` - Main Python implementation
- `requirements.txt` - Python dependencies
- `README.md` - This documentation file

## Data Structures

### SWP_Profile_TypeDef
Contains configuration parameters like:
- Start/Stop/Center frequencies
- Reference level, RBW, VBW
- Sweep time and trace parameters
- Various mode settings

### SWP_TraceInfo_TypeDef
Contains trace-specific information:
- Trace points and sweep parameters
- Frequency and bandwidth settings
- Data format information

### MeasAuxInfo_TypeDef
Contains measurement auxiliary data:
- Maximum power and index
- Temperature and state information
- Timestamps and GPS coordinates

## Key Functions

1. `swp_recording_configuration_info()` - Reads configuration and trace info from file
2. `swp_recording_power_spectral_density_info()` - Extracts frequency/power data
3. `swp_mode_playback()` - Main processing function

## Differences from C++ Version

- Uses Python's `struct` module for binary data handling
- Implements msgpack unpacking with Python's msgpack library
- Uses Python lists instead of C++ vectors
- Simplified error handling with exceptions
- More Pythonic code structure with dataclasses

## Error Codes

- 0: Success
- -1000: File opening failed
- -1001: Configuration reading failed
- -1002: Incorrect file version (not version 55)

## Notes

- The program expects spectrum files in version 55 format
- All byte order conversions are handled automatically
- The data directory will be created if it doesn't exist
