"""
整合分析器演示脚本
展示如何使用 IntegratedIQMapAnalyzer 类的各种功能
"""

import os
from integrated_iq_map_analyzer import IntegratedIQMapAnalyzer, find_wav_files

def demo_basic_analysis():
    """基本分析演示"""
    print("=== 基本分析演示 ===")
    
    # 查找WAV文件
    wav_files = find_wav_files()
    if not wav_files:
        print("没有找到WAV文件")
        return
    
    # 使用第一个文件进行演示
    wav_file = wav_files[0]
    print(f"使用文件: {wav_file}")
    
    # 创建分析器
    analyzer = IntegratedIQMapAnalyzer(wav_file)
    
    # 生成完整分析
    output_file = f"demo_basic_{os.path.splitext(wav_file)[0]}.png"
    success = analyzer.generate_map_with_spectrogram(output_file)
    
    if success:
        print(f"✅ 基本分析完成，结果保存到: {output_file}")
    else:
        print("❌ 基本分析失败")
    
    return success

def demo_gnss_extraction():
    """GNSS信息提取演示"""
    print("\n=== GNSS信息提取演示 ===")
    
    wav_files = find_wav_files()
    if not wav_files:
        print("没有找到WAV文件")
        return
    
    wav_file = wav_files[0]
    print(f"分析文件: {wav_file}")
    
    # 创建分析器
    analyzer = IntegratedIQMapAnalyzer(wav_file)
    
    # 提取GNSS信息
    gnss_data = analyzer.extract_gnss_info()
    
    if gnss_data:
        print(f"✅ 成功提取 {len(gnss_data)} 个数据包的GNSS信息:")
        for i, gnss in enumerate(gnss_data):
            print(f"  数据包 {i}: 经度={gnss['Longitude']:.6f}, 纬度={gnss['Latitude']:.6f}")
    else:
        print("❌ GNSS信息提取失败")
    
    return gnss_data is not None

def demo_spectrogram_only():
    """仅生成时频图演示"""
    print("\n=== 时频图生成演示 ===")
    
    wav_files = find_wav_files()
    if not wav_files:
        print("没有找到WAV文件")
        return
    
    wav_file = wav_files[0]
    print(f"分析文件: {wav_file}")
    
    # 创建分析器
    analyzer = IntegratedIQMapAnalyzer(wav_file)
    
    # 提取GNSS信息以获取IQ数据
    gnss_data = analyzer.extract_gnss_info()
    
    if gnss_data and len(gnss_data) > 0:
        # 生成时频图
        iq_data = gnss_data[0]['IQData']
        spectrogram_img = analyzer.generate_spectrogram(iq_data, "演示时频图")
        
        if spectrogram_img:
            # 保存时频图
            output_file = f"demo_spectrogram_{os.path.splitext(wav_file)[0]}.png"
            spectrogram_img.save(output_file)
            print(f"✅ 时频图已保存到: {output_file}")
            return True
        else:
            print("❌ 时频图生成失败")
    else:
        print("❌ 无法获取IQ数据")
    
    return False

def demo_batch_analysis():
    """批量分析演示"""
    print("\n=== 批量分析演示 ===")
    
    wav_files = find_wav_files()
    if len(wav_files) < 2:
        print("需要至少2个WAV文件进行批量分析演示")
        return
    
    # 分析前3个文件（如果有的话）
    files_to_analyze = wav_files[:min(3, len(wav_files))]
    print(f"将分析 {len(files_to_analyze)} 个文件:")
    
    results = []
    for i, wav_file in enumerate(files_to_analyze):
        print(f"\n处理文件 {i+1}/{len(files_to_analyze)}: {wav_file}")
        
        analyzer = IntegratedIQMapAnalyzer(wav_file)
        output_file = f"demo_batch_{i+1}_{os.path.splitext(wav_file)[0]}.png"
        
        success = analyzer.generate_map_with_spectrogram(output_file)
        results.append((wav_file, success, output_file))
        
        if success:
            print(f"  ✅ 成功: {output_file}")
        else:
            print(f"  ❌ 失败: {wav_file}")
    
    # 总结结果
    successful = sum(1 for _, success, _ in results if success)
    print(f"\n批量分析完成: {successful}/{len(results)} 个文件成功处理")
    
    return successful > 0

def demo_error_handling():
    """错误处理演示"""
    print("\n=== 错误处理演示 ===")
    
    # 测试不存在的文件
    print("测试不存在的文件...")
    analyzer = IntegratedIQMapAnalyzer("nonexistent_file.wav")
    success = analyzer.generate_map_with_spectrogram("error_test.png")
    
    if not success:
        print("✅ 正确处理了文件不存在的情况")
    else:
        print("❌ 错误处理有问题")
    
    return True

def main():
    """主演示函数"""
    print("🚀 整合分析器功能演示")
    print("=" * 50)
    
    # 检查是否有WAV文件
    wav_files = find_wav_files()
    if not wav_files:
        print("❌ 当前目录中没有找到WAV文件")
        print("请确保有.wav文件在当前目录中")
        return
    
    print(f"找到 {len(wav_files)} 个WAV文件:")
    for i, file in enumerate(wav_files, 1):
        print(f"  {i}. {file}")
    
    # 运行各种演示
    demos = [
        ("基本分析", demo_basic_analysis),
        ("GNSS信息提取", demo_gnss_extraction),
        ("时频图生成", demo_spectrogram_only),
        ("批量分析", demo_batch_analysis),
        ("错误处理", demo_error_handling)
    ]
    
    results = {}
    for demo_name, demo_func in demos:
        try:
            print(f"\n{'='*20}")
            result = demo_func()
            results[demo_name] = result
        except Exception as e:
            print(f"❌ {demo_name} 演示出错: {e}")
            results[demo_name] = False
    
    # 总结
    print(f"\n{'='*50}")
    print("🎯 演示总结:")
    for demo_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {demo_name}: {status}")
    
    successful_demos = sum(1 for success in results.values() if success)
    print(f"\n总体结果: {successful_demos}/{len(demos)} 个演示成功")
    
    if successful_demos == len(demos):
        print("🎉 所有演示都成功完成！")
    else:
        print("⚠️ 部分演示失败，请检查错误信息")

if __name__ == "__main__":
    main()
