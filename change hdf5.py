import pandas as pd
import numpy as np
import h5py
import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('iq_data_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def read_csv_file(file_path):
    """
    Read a CSV file generated by main.py and extract I_data, Q_data, CenterFreq_Hz, and IQSampleRate
    
    Args:
        file_path (str): Path to the CSV file
        
    Returns:
        dict: Dictionary containing I_data, Q_data, CenterFreq_Hz, and IQSampleRate
    """
    try:
        # Read the header information (first 4 lines)
        header_info = {}
        with open(file_path, 'r') as f:
            for i in range(4):
                line = f.readline().strip()
                if i > 0:  # Skip the first line (Device UID)
                    key, value = line.split(':,')
                    header_info[key] = float(value)
        
        # Read the I_data and Q_data
        data = pd.read_csv(file_path, skiprows=4)
        
        return {
            'I_data': data['I_Data'].values,
            'Q_data': data['Q_Data'].values,
            'CenterFreq_Hz': header_info.get('CenterFreq_Hz', 0),
            'IQSampleRate': header_info.get('IQSampleRate', 0)
        }
    except Exception as e:
        logger.error(f"Error reading CSV file {file_path}: {e}")
        sys.exit(1)

def save_to_hdf5(file_path, data_dict):
    """
    Save multiple datasets to an HDF5 file
    
    Args:
        file_path (str): Path to the HDF5 file to be created
        data_dict (dict): Dictionary containing data from multiple CSV files
    """
    try:
        with h5py.File(file_path, 'w') as f:
            # Create a group for each CSV file
            for file_name, data in data_dict.items():
                group = f.create_group(file_name)
                
                # Save I_data and Q_data as datasets
                group.create_dataset('I_data', data=data['I_data'])
                group.create_dataset('Q_data', data=data['Q_data'])
                
                # Save metadata as attributes
                group.attrs['CenterFreq_Hz'] = data['CenterFreq_Hz']
                group.attrs['IQSampleRate'] = data['IQSampleRate']
    except Exception as e:
        logger.error(f"Error saving to HDF5 file {file_path}: {e}")
        sys.exit(1)

def print_summary(data_dict):
    """
    Print a summary of the data
    
    Args:
        data_dict (dict): Dictionary containing data from multiple CSV files
    """
    logger.info("Summary of saved data:")
    for file_name, data in data_dict.items():
        logger.info(f"Group: {file_name}")
        logger.info(f"  CenterFreq_Hz: {data['CenterFreq_Hz']}")
        logger.info(f"  IQSampleRate: {data['IQSampleRate']}")
        logger.info(f"  I_data shape: {data['I_data'].shape}")
        logger.info(f"  Q_data shape: {data['Q_data'].shape}")

def main():
    """
    Main function to read CSV files and save data to HDF5
    """
    # File paths
    csv_file1 = '0053_20250630_231812.part1.iq.csv'
    csv_file2 = '0053_20250630_231810.part1.iq.csv'
    hdf5_file = 'iq_data.h5'
    
    logger.info(f"Starting processing of CSV files: {csv_file1} and {csv_file2}")
    
    # Check if input files exist
    for file_path in [csv_file1, csv_file2]:
        if not os.path.exists(file_path):
            logger.error(f"Error: File {file_path} does not exist.")
            sys.exit(1)
    
    # Read data from CSV files
    logger.info(f"Reading data from {csv_file1}...")
    data1 = read_csv_file(csv_file1)
    logger.info(f"Reading data from {csv_file2}...")
    data2 = read_csv_file(csv_file2)
    
    # Print some information about the data
    logger.info(f"File: {csv_file1}")
    logger.info(f"  CenterFreq_Hz: {data1['CenterFreq_Hz']}")
    logger.info(f"  IQSampleRate: {data1['IQSampleRate']}")
    logger.info(f"  Number of samples: {len(data1['I_data'])}")
    
    logger.info(f"File: {csv_file2}")
    logger.info(f"  CenterFreq_Hz: {data2['CenterFreq_Hz']}")
    logger.info(f"  IQSampleRate: {data2['IQSampleRate']}")
    logger.info(f"  Number of samples: {len(data2['I_data'])}")
    
    # Create a dictionary with data from both files
    data_dict = {
        os.path.basename(csv_file1).replace('.csv', ''): data1,
        os.path.basename(csv_file2).replace('.csv', ''): data2
    }
    
    # Save data to HDF5 file
    logger.info(f"Saving data to {hdf5_file}...")
    save_to_hdf5(hdf5_file, data_dict)
    logger.info(f"Data successfully saved to {hdf5_file}")
    
    # Print a summary of what was saved
    print_summary(data_dict)
    
    logger.info("Processing completed successfully")

if __name__ == "__main__":
    main()
