#include <stdio.h>
#include <vector>
#include "htra_api.h"
#include <iostream>
#include <fstream>
#include <string>
#include <msgpack.h>
#include <filesystem>
#include <iomanip> 
#define _CRT_SECURE_NO_WARNINGS
using namespace std;

/*�ֽ���ת����С��ת����*/
//64λ�޷��������ֽ���ת
uint64_t swap_641(uint64_t value) {
	uint64_t result = 0;
	result |= (value & 0xff00000000000000) >> 56;
	result |= (value & 0x00ff000000000000) >> 40;
	result |= (value & 0x0000ff0000000000) >> 24;
	result |= (value & 0x000000ff00000000) >> 8;
	result |= (value & 0x00000000ff000000) << 8;
	result |= (value & 0x0000000000ff0000) << 24;
	result |= (value & 0x000000000000ff00) << 40;
	result |= (value & 0x00000000000000ff) << 56;
	return result;
}

//32λ�޷��������ֽ���ת
uint32_t swap_321(uint32_t val)
{
	return (((val & 0xff000000) >> 24) \
		| ((val & 0x00ff0000) >> 8) \
		| ((val & 0x0000ff00) << 8) \
		| ((val & 0x000000ff) << 24));
}

//16λ޷��������ֽ���ת
uint16_t swap_161(uint16_t val)
{
	return ((val & 0x00ff) << 8) | ((val & 0xff00) >> 8);
}

//float������ �ֽ���ת
float convertEndianFloat1(float value) {
	union {
		float f;
		uint32_t i;
	} u;
	u.f = value;
	u.i = swap_321(u.i);
	return u.f;
}

//double������ �ֽ���ת
double convertEndianDouble1(double value) {
	union {
		double d;
		uint64_t l;
	} u;
	u.d = value;
	u.l = swap_641(u.l);
	return u.d;
}


//��ȡIQSģʽ���õ������Ϣ���豸�������Ϣ
int GetIQSWavFileInfo1(const char* FilePath, IQS_Profile_TypeDef* IQS_Profile, IQS_StreamInfo_TypeDef* IQS_StreamInfo, DeviceInfo_TypeDef* DeviceInfo, uint32_t* IQSize)
{
	int Status = 0;

	//�򿪼�¼�ļ�
	fstream File;
	string FilePath_s = FilePath;
	File.open(FilePath_s, ios::in | ios::binary);

	if (!File.is_open())
	{
		Status = -1009;
		return Status;
	}

	uint16_t StructSize = 0;
	uint32_t IQ_Size = 0;
	char InfoSize_2[2];
	char InfoSize_4[4];
	char InfoSize_8[8];

	/*��ȡIQS_Profile��IQS_StreamInfo*/

	//��ȡIQS_Profile��IQS_StreamInfo  �ṹ���ֽڳ���  �������ṹ����ܴ�С��  
	File.seekp(108, ios::beg);
	File.read(InfoSize_2, sizeof(InfoSize_2));
	memcpy(&StructSize, InfoSize_2, sizeof(InfoSize_2));
	StructSize = swap_161(StructSize); //����ȡ�����ֽڳ��ȴӴ���ֽ���ת��ΪС���ֽ��������ֽ���

	//��ȡIQS_Profile �� IQS_StreamInfo �����ݡ�
	char* IQSProAndStream = new char[StructSize];
	File.read(IQSProAndStream, StructSize);

	//ʹ�� msgpack �Զ�ȡ�������ݽ��н�������ռ�¼�ļ���ʽ��˵������һ�����ļ��еĲ�������IQS_Profile �� IQS_StreamInfo�е����ݾ���С���ֽ����¼��
	msgpack_unpacked unpacked;
	msgpack_unpacked_init(&unpacked);
	size_t off = 0;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off); //��������Ƶ�ʣ���λ��Hz�����������õ��� 64 λ��������double ���ͣ���ֵ�� IQS_Profile �� CenterFreq_Hz
	IQS_Profile->CenterFreq_Hz = unpacked.data.via.f64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off); //�����ο���ƽֵ����λ��dBm������������ 64 λ��������ֵ�� IQS_Profile �� RefLevel_dBm
	IQS_Profile->RefLevel_dBm = unpacked.data.via.f64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off); //������ȡ�������������� 64 λ�޷�������ת��Ϊ 32 λ�޷�������������ֵ�� IQS_Profile �� DecimateFactor ��Ա����
	IQS_Profile->DecimateFactor = static_cast<uint32_t>(unpacked.data.via.u64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off); //���½���ͬ�ϣ�����������������Ϳɲμ�����¼�ļ���ʽ˵����
	IQS_Profile->RxPort = static_cast<RxPort_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->BusTimeout_ms = static_cast<uint32_t>(unpacked.data.via.u64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->TriggerSource = static_cast<IQS_TriggerSource_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->TriggerEdge = static_cast<TriggerEdge_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->TriggerMode = static_cast<TriggerMode_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->TriggerLength = unpacked.data.via.u64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->TriggerOutMode = static_cast<TriggerOutMode_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->TriggerOutPulsePolarity = static_cast<TriggerOutPulsePolarity_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->TriggerLevel_dBm = unpacked.data.via.f64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->TriggerLevel_SafeTime = unpacked.data.via.f64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->TriggerDelay = (TriggerOutMode_TypeDef)unpacked.data.via.f64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->PreTriggerTime = (TriggerOutPulsePolarity_TypeDef)unpacked.data.via.f64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->TriggerTimerSync = static_cast<TriggerTimerSync_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->TriggerTimer_Period = unpacked.data.via.f64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->EnableReTrigger = static_cast<uint8_t>(unpacked.data.via.u64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->ReTrigger_Period = unpacked.data.via.f64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->ReTrigger_Count = static_cast<uint16_t>(unpacked.data.via.u64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->DataFormat = static_cast<DataFormat_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->GainStrategy = static_cast<GainStrategy_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->Preamplifier = static_cast<PreamplifierState_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->AnalogIFBWGrade = static_cast<uint8_t>(unpacked.data.via.u64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->IFGainGrade = static_cast<uint8_t>(unpacked.data.via.u64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->EnableDebugMode = static_cast<uint8_t>(unpacked.data.via.u64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->ReferenceClockSource = static_cast<ReferenceClockSource_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->ReferenceClockFrequency = unpacked.data.via.f64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->EnableReferenceClockOut = static_cast<uint8_t>(unpacked.data.via.u64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->SystemClockSource = static_cast<SystemClockSource_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->ExternalSystemClockFrequency = unpacked.data.via.f64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->NativeIQSampleRate_SPS = unpacked.data.via.f64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->EnableIFAGC = static_cast<uint8_t>(unpacked.data.via.u64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->Atten = static_cast<int8_t>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->DCCancelerMode = static_cast<DCCancelerMode_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->QDCMode = static_cast<QDCMode_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->QDCIGain = static_cast<float>(unpacked.data.via.f64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->QDCQGain = static_cast<float>(unpacked.data.via.f64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->QDCPhaseComp = static_cast<float>(unpacked.data.via.f64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->DCCIOffset = static_cast<int8_t>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->DCCQOffset = static_cast<int8_t>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_Profile->LOOptimization = static_cast<LOOptimization_TypeDef>(unpacked.data.via.i64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_StreamInfo->Bandwidth = unpacked.data.via.f64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_StreamInfo->IQSampleRate = unpacked.data.via.f64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_StreamInfo->PacketCount = unpacked.data.via.u64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_StreamInfo->StreamSamples = unpacked.data.via.u64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_StreamInfo->StreamDataSize = unpacked.data.via.u64;

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_StreamInfo->PacketSamples = static_cast<uint32_t>(unpacked.data.via.u64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_StreamInfo->PacketDataSize = static_cast<uint32_t>(unpacked.data.via.u64);

	msgpack_unpack_next(&unpacked, IQSProAndStream, (size_t)StructSize, &off);
	IQS_StreamInfo->GainParameter = static_cast<uint32_t>(unpacked.data.via.u64);

	/*��ȡDeviceInfo*/
	File.read(InfoSize_2, sizeof(InfoSize_2)); //DeviceInfo�ṹ���ֽڳ���

	File.read(InfoSize_8, sizeof(InfoSize_8)); //��ȡ�豸UID
	memcpy(&DeviceInfo->DeviceUID, InfoSize_8, sizeof(InfoSize_8));
	DeviceInfo->DeviceUID = swap_641(DeviceInfo->DeviceUID); //���תС��

	File.read(InfoSize_2, sizeof(InfoSize_2));       //��ȡ�豸����
	memcpy(&DeviceInfo->Model, InfoSize_2, sizeof(InfoSize_2));
	DeviceInfo->Model = swap_161(DeviceInfo->Model); //���תС��

	File.read(InfoSize_2, sizeof(InfoSize_2));                           //��ȡ�豸Ӳ���汾
	memcpy(&DeviceInfo->HardwareVersion, InfoSize_2, sizeof(InfoSize_2));
	DeviceInfo->HardwareVersion = swap_161(DeviceInfo->HardwareVersion); //���תС��

	File.read(InfoSize_4, sizeof(InfoSize_4));                 //��ȡ�豸MCU�̼��汾
	memcpy(&DeviceInfo->MFWVersion, InfoSize_4, sizeof(InfoSize_4));
	DeviceInfo->MFWVersion = swap_321(DeviceInfo->MFWVersion); //���תС��

	File.read(InfoSize_4, sizeof(InfoSize_4));                 //��ȡ�豸FPGA�̼��汾
	memcpy(&DeviceInfo->FFWVersion, InfoSize_4, sizeof(InfoSize_4));
	DeviceInfo->FFWVersion = swap_321(DeviceInfo->FFWVersion); //���תС��

	//��ȡIQ�����ܳ���
	File.seekp(25 * 1024 * 1024 + 404, ios::beg); //���ļ�ָ���ƶ����ļ���data��ʽ�鳤�ȴ�
	File.read(InfoSize_4, sizeof(InfoSize_4));    //��ȡ��ʽ��ĳ���
	memcpy(&IQ_Size, InfoSize_4, sizeof(InfoSize_4));

	*IQSize = IQ_Size / 64968; //��ȡ��¼���ݵ��ܰ�����ÿһ����64968���ֽڣ�

	File.close();
	return Status;
}

//��ȡIQSģʽ�½�֯��IQ�����������������Է��ȣ�V��λ���ı������ӣ�ScaleToV����������ص���Ϣ 
int GetIQSWavFileData1(IQS_Profile_TypeDef* IQS_Profile, const char* FilePath, const uint32_t PacketNum, IQStream_TypeDef* IQStream, int16_t IQData[])
{
	int Status = 0;
	fstream File;
	string FilePath_s = FilePath;                 //���ļ�·��ת��Ϊ string ����
	File.open(FilePath_s, ios::in | ios::binary); //��.wav�ļ����Զ�������ʽ���ж�ȡ��

	if (!File.is_open())
	{
		Status = -1009;
		return Status;
	}

	// �������ڶ�ȡ�ļ����ݵĻ�����
	char InfoSize_1[1];
	char InfoSize_2[2];
	char InfoSize_4[4];
	char InfoSize_8[8];

	/*��ȡIQS_TriggerInfo*/
	//��ת��ÿһ����IQS_TriggerInfo�ṹ���ֽڳ���λ�ã����� PacketNum �ͽṹ���ֽڳ��ȶ�λ��ǰ�ṹ��λ��
	File.seekp(408 + PacketNum * (405), ios::beg); //��ÿһ����405���ֽڣ�
	File.read(InfoSize_2, sizeof(InfoSize_2));     //IQS_TriggerInfo Struct Size

	File.read(InfoSize_8, sizeof(InfoSize_8));
	memcpy(&IQStream->IQS_TriggerInfo.SysTimerCountOfFirstDataPoint, InfoSize_8, sizeof(InfoSize_8));                                         //��ȡ��ǰ�����׸����ݵ��Ӧ��ϵͳʱ���
	IQStream->IQS_TriggerInfo.SysTimerCountOfFirstDataPoint = convertEndianDouble1(IQStream->IQS_TriggerInfo.SysTimerCountOfFirstDataPoint); // ���תС�ˣ�.wav�ļ����Դ�˵���ʽ�洢�������λ��С����ʾ������Ҫ���ж����ת����

	File.read(InfoSize_2, sizeof(InfoSize_2));
	memcpy(&IQStream->IQS_TriggerInfo.InPacketTriggeredDataSize, InfoSize_2, sizeof(InfoSize_2));                        //��ȡ��ǰ������Ч�������ݵ��ֽ���
	IQStream->IQS_TriggerInfo.InPacketTriggeredDataSize = swap_161(IQStream->IQS_TriggerInfo.InPacketTriggeredDataSize); //���תС��

	File.read(InfoSize_2, sizeof(InfoSize_2));
	memcpy(&IQStream->IQS_TriggerInfo.InPacketTriggerEdges, InfoSize_2, sizeof(InfoSize_2));                   //��ȡ��ǰ�����������Ĵ������ظ���
	IQStream->IQS_TriggerInfo.InPacketTriggerEdges = swap_161(IQStream->IQS_TriggerInfo.InPacketTriggerEdges); //���תС��

	//���½���ͬ�ϣ���������������Ϳɲμ�����¼�ļ���ʽ˵����
	for (int i = 0; i < 25; i++)
	{
		File.read(InfoSize_4, sizeof(InfoSize_4));
		memcpy(&IQStream->IQS_TriggerInfo.StartDataIndexOfTriggerEdges[i], InfoSize_4, sizeof(InfoSize_4));
	}
	for (int i = 0; i < 25; i++)
	{
		File.read(InfoSize_8, sizeof(InfoSize_8));
		memcpy(&IQStream->IQS_TriggerInfo.SysTimerCountOfEdges[i], InfoSize_8, sizeof(InfoSize_8));
	}
	for (int i = 0; i < 25; i++)
	{
		File.read(InfoSize_1, sizeof(InfoSize_1));
		memcpy(&IQStream->IQS_TriggerInfo.EdgeType[i], InfoSize_1, sizeof(InfoSize_1));
	}

	/*��ȡDeviceState*/
	File.read(InfoSize_2, sizeof(InfoSize_2)); //DeviceState struct size

	File.read(InfoSize_2, sizeof(InfoSize_2));
	memcpy(&IQStream->DeviceState.Temperature, InfoSize_2, sizeof(InfoSize_2));      //��ȡ�¶���Ϣ
	IQStream->DeviceState.Temperature = swap_161(IQStream->DeviceState.Temperature); //���תС��

	File.read(InfoSize_2, sizeof(InfoSize_2));
	memcpy(&IQStream->DeviceState.RFState, InfoSize_2, sizeof(InfoSize_2));  //��ȡ��Ƶ״̬
	IQStream->DeviceState.RFState = swap_161(IQStream->DeviceState.RFState); //���תС��

	File.read(InfoSize_2, sizeof(InfoSize_2));
	memcpy(&IQStream->DeviceState.BBState, InfoSize_2, sizeof(InfoSize_2));  //��ȡ����״̬
	IQStream->DeviceState.BBState = swap_161(IQStream->DeviceState.BBState); //���תС��

	File.read(InfoSize_8, sizeof(InfoSize_8)); //���½���ͬ��
	memcpy(&IQStream->DeviceState.AbsoluteTimeStamp, InfoSize_8, sizeof(InfoSize_8));
	IQStream->DeviceState.AbsoluteTimeStamp = convertEndianDouble1(IQStream->DeviceState.AbsoluteTimeStamp);

	File.read(InfoSize_4, sizeof(InfoSize_4));
	memcpy(&IQStream->DeviceState.Latitude, InfoSize_4, sizeof(InfoSize_4));
	IQStream->DeviceState.Latitude = convertEndianFloat1(IQStream->DeviceState.Latitude);

	File.read(InfoSize_4, sizeof(InfoSize_4));
	memcpy(&IQStream->DeviceState.Longitude, InfoSize_4, sizeof(InfoSize_4));
	IQStream->DeviceState.Longitude = convertEndianFloat1(IQStream->DeviceState.Longitude);

	File.read(InfoSize_2, sizeof(InfoSize_2));
	memcpy(&IQStream->DeviceState.GainPattern, InfoSize_2, sizeof(InfoSize_2));
	IQStream->DeviceState.GainPattern = swap_161(IQStream->DeviceState.GainPattern);

	File.read(InfoSize_8, sizeof(InfoSize_8));
	memcpy(&IQStream->DeviceState.RFCFreq, InfoSize_8, sizeof(InfoSize_8));
	IQStream->DeviceState.RFCFreq = swap_641(IQStream->DeviceState.RFCFreq);

	File.read(InfoSize_4, sizeof(InfoSize_4));
	memcpy(&IQStream->DeviceState.ConvertPattern, InfoSize_4, sizeof(InfoSize_4));
	IQStream->DeviceState.ConvertPattern = swap_321(IQStream->DeviceState.ConvertPattern);

	File.read(InfoSize_4, sizeof(InfoSize_4));
	memcpy(&IQStream->DeviceState.NCOFTW, InfoSize_4, sizeof(InfoSize_4));
	IQStream->DeviceState.NCOFTW = swap_321(IQStream->DeviceState.NCOFTW);

	File.read(InfoSize_4, sizeof(InfoSize_4));
	memcpy(&IQStream->DeviceState.SampleRate, InfoSize_4, sizeof(InfoSize_4));
	IQStream->DeviceState.SampleRate = swap_321(IQStream->DeviceState.SampleRate);

	File.read(InfoSize_2, sizeof(InfoSize_2));
	memcpy(&IQStream->DeviceState.CPU_BCFlag, InfoSize_2, sizeof(InfoSize_2));
	IQStream->DeviceState.CPU_BCFlag = swap_161(IQStream->DeviceState.CPU_BCFlag);

	File.read(InfoSize_2, sizeof(InfoSize_2));
	memcpy(&IQStream->DeviceState.IFOverflow, InfoSize_2, sizeof(InfoSize_2));
	IQStream->DeviceState.IFOverflow = swap_161(IQStream->DeviceState.IFOverflow);

	File.read(InfoSize_2, sizeof(InfoSize_2));
	memcpy(&IQStream->DeviceState.DecimateFactor, InfoSize_2, sizeof(InfoSize_2));
	IQStream->DeviceState.DecimateFactor = swap_161(IQStream->DeviceState.DecimateFactor);

	File.read(InfoSize_2, sizeof(InfoSize_2));
	memcpy(&IQStream->DeviceState.OptionState, InfoSize_2, sizeof(InfoSize_2));
	IQStream->DeviceState.OptionState = swap_161(IQStream->DeviceState.OptionState);

	/*��ȡIQS_ScaleToV  MaxPower_dBm MaxIndex */
	File.read(InfoSize_4, sizeof(InfoSize_4));
	memcpy(&IQStream->IQS_ScaleToV, InfoSize_4, sizeof(InfoSize_4));
	IQStream->IQS_ScaleToV = convertEndianFloat1(IQStream->IQS_ScaleToV);

	File.read(InfoSize_4, sizeof(InfoSize_4));
	memcpy(&IQStream->MaxPower_dBm, InfoSize_4, sizeof(InfoSize_4));
	IQStream->MaxPower_dBm = convertEndianFloat1(IQStream->MaxPower_dBm);

	File.read(InfoSize_4, sizeof(InfoSize_4));
	memcpy(&IQStream->MaxIndex, InfoSize_4, sizeof(InfoSize_4));
	IQStream->MaxIndex = swap_321(IQStream->MaxIndex);

	//��ȡIQ�����ܳ���
	File.seekp(25 * 1024 * 1024 + 404, ios::beg); //���ļ�ָ�붨λ��data��ʽ�쳤�ȴ�
	File.read(InfoSize_4, sizeof(InfoSize_4));    //��ȡIQ�����ܳ���

	int format = 0;

	//�ж�IQ���ݸ�ʽ
	switch (IQS_Profile->DataFormat)
	{
	case  Complex32bit:
		format = 4;
		break;
	case Complex16bit:
		format = 2;
		break;
	case Complex8bit:
		format = 1;
		break;
	default:
		break;
	}

	//��ȡIQ����
	File.seekp(25 * 1024 * 1024 + 408 + PacketNum * 64968, ios::beg); //���ļ�ָ�붨λ��ָ��packetnum�Ŀ�ͷ

	for (uint32_t i = 0; i < (64968 / format); i++)
	{
		File.read(InfoSize_2, sizeof(InfoSize_2));
		memcpy(&IQData[i], InfoSize_2, sizeof(InfoSize_2));
	}

	File.close();
	return Status;
}

//��ȡwav�ļ��е������Ϣ
int IQSMode_WavToCsv()
{
	int Status = 0;
	
	char path[] = "D:\nxm-60\Windows\SAStudio4\data\BPSKQ2\0053_20250630_232013.part1.iq.wav"; //�밴���޸ļ�¼�ļ������磺0022_20241225_164858.part1.iq.wav

	IQStream_TypeDef IQStream;
	DeviceInfo_TypeDef DeviceInfo;
	IQS_Profile_TypeDef IQS_Profile;
	IQS_StreamInfo_TypeDef StreamInfo;
	uint32_t PacketCount = 0;

	string FilePath_last = "./data/"; // �ļ�����·��Ϊ ./data/

// ���Ŀ¼�Ƿ��Ѵ��ڣ�����������򴴽�
	if (!(filesystem::exists(FilePath_last))) {
		// ���Դ���Ŀ¼���丸Ŀ¼
		filesystem::create_directories(FilePath_last);
		cout << "Data folder path: Htra_RecordingandPlayBack\\data" << endl;
		if (filesystem::create_directories(FilePath_last)) {
			cout << "Failed to create directory \"" << FilePath_last << "\"." << endl;
			return 0;
		}
	}

	Status = GetIQSWavFileInfo1(path, &IQS_Profile, &StreamInfo, &DeviceInfo, &PacketCount); //��ȡIQS�ṹ����Ϣ��DeviceInfo
	
	IQStream.IQS_Profile = IQS_Profile;
	IQStream.IQS_StreamInfo = StreamInfo;
	IQStream.DeviceInfo = DeviceInfo;

	vector<int16_t> IQ_Data(StreamInfo.PacketSamples * 2);          //�������ڴ��һ�����ݵ��ڴ档
	vector<int16_t> I_Data(StreamInfo.PacketSamples * PacketCount); //�������ڴ��I·���ݵ��ڴ档
	vector<int16_t> Q_Data(StreamInfo.PacketSamples * PacketCount); //�������ڴ��Q·���ݵ��ڴ档
	
	char FileName[255]; // �����ļ����ַ�����

	sprintf_s(FileName, "./data/IQSMode_Data.csv"); // ��ʽ���ļ���Ϊ "IQSMode_Data.csv"

	ofstream File1; // ����һ��ofstream�����������ļ��洢������IQ����
	
	File1.open(FileName, ios::out); // ��д��ģʽ���ļ�

	File1 << "Device UID:," << std::hex << DeviceInfo.DeviceUID << "\n"; //CSV�ļ���һ�У��豸 UID
	File1 << std::dec;                                                   //�ָ�Ĭ�ϵ�ʮ���������ʽ
	File1 << "CenterFreq_Hz:," <<IQS_Profile.CenterFreq_Hz << "\n";      //�ڶ��У�����Ƶ��
	File1 << "IQSampleRate:," << StreamInfo.IQSampleRate << "\n";        //�����У�������
	File1 << "DecimateFactor:," << IQS_Profile.DecimateFactor << "\n";   //�����У���ȡ����
	File1 << "I_Data,Q_Data\n";                                          //�����У�IQ��·����

	for (uint32_t PacketNum = 0; PacketNum < PacketCount; PacketNum++) 
	{
		Status = GetIQSWavFileData1(&IQS_Profile, path, PacketNum, &IQStream, IQ_Data.data());  // ��ȡ����

		//if (PacketNum == 0)
		//{
		//	// ��ȡ��ǰд�����ļ�ָ��λ��
		//	streampos pos = File1.tellp();

		//	File1 << "IQS_ScaleToV:," << IQStream.IQS_ScaleToV << "\n";
		//}

		uint32_t Points = StreamInfo.PacketSamples;

		if (PacketNum == PacketCount - 1 && StreamInfo.StreamSamples % StreamInfo.PacketSamples != 0) {
			// �������һ������һ������16242���㣩��ֻ��Ҫѭ������һ���ĵ���
			Points = StreamInfo.StreamSamples % (uint64_t)StreamInfo.PacketSamples;
		}

		for (uint32_t i = 0; i < Points; i++) {
			Q_Data[i + StreamInfo.PacketSamples * PacketNum] = IQ_Data[i * 2];
			I_Data[i + StreamInfo.PacketSamples * PacketNum] = IQ_Data[i * 2 + 1];
			// д��CSV���ݣ�ʹ�ö��ŷָ�
			File1 << IQ_Data[i * 2 + 1] << "," << IQ_Data[i * 2] << "\n";
		}
	}

	// �ر��ļ��������沢�ͷ���Դ
	File1.close();
	return 0;
}
