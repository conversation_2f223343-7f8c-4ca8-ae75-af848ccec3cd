@echo off
chcp 65001 >nul
echo ========================================
echo IQS WAV to CSV 转换器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.6或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 检测到Python版本:
python --version

REM 检查依赖包
echo.
echo 检查依赖包...
python -c "import msgpack, pandas" >nul 2>&1
if errorlevel 1 (
    echo 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
) else (
    echo 依赖包检查通过
)

REM 运行转换器
echo.
echo 开始运行转换器...
echo ========================================
python iqs_wav_to_csv.py

echo.
echo ========================================
echo 转换完成！
echo ========================================
pause
