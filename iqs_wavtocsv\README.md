# IQS WAV to CSV 转换器

这是一个将IQS模式的WAV文件转换为CSV文件的Python工具，完全复制了原始C++代码的功能，并增加了批量处理能力。

## 功能特性

- **完全兼容**: 与原始C++代码功能一模一样
- **批量处理**: 支持处理指定文件夹下所有子文件夹中的WAV文件
- **自动目录创建**: 自动在每个WAV文件所在目录创建`data`子文件夹存放CSV文件
- **详细日志**: 提供详细的处理进度和错误信息
- **错误处理**: 单个文件转换失败不会影响其他文件的处理

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 修改配置

编辑 `iqs_wav_to_csv.py` 文件中的 `main()` 函数，修改以下参数：

```python
ROOT_DIR = r"D:\nxm-60\Windows\SAStudio4\data"  # 修改为你的根目录路径
OUTPUT_BASE_DIR = None  # 可选：指定输出基础目录
```

### 2. 运行程序

```bash
python iqs_wav_to_csv.py
```

### 3. 程序化使用

```python
from iqs_wav_to_csv import IQSWavConverter

# 创建转换器实例
converter = IQSWavConverter()

# 转换单个文件
csv_file = converter.convert_wav_to_csv("path/to/your/file.wav")

# 批量转换目录
converted_files = converter.batch_convert_directory("D:/nxm-60/Windows/SAStudio4/data")
```

## 输出格式

生成的CSV文件包含以下信息：

1. **设备信息**:
   - Device UID (十六进制格式)
   - 中心频率 (Hz)
   - IQ采样率
   - 抽取因子

2. **IQ数据**:
   - I_Data: I路数据
   - Q_Data: Q路数据

## 文件结构

转换后的文件将保存在原WAV文件所在目录的`data`子文件夹中，文件名格式为：
`{原文件名}_IQSMode_Data.csv`

例如：
```
原文件: D:\nxm-60\Windows\SAStudio4\data\BPSKQ2\0053_20250630_232013.part1.iq.wav
输出文件: D:\nxm-60\Windows\SAStudio4\data\BPSKQ2\data\0053_20250630_232013.part1.iq_IQSMode_Data.csv
```

## 技术实现

### 核心功能

1. **字节序转换**: 实现了16位、32位、64位整数以及float、double类型的大小端转换
2. **msgpack解析**: 使用msgpack库解析WAV文件中的结构化数据
3. **二进制数据读取**: 精确按照原C++代码的方式读取文件数据
4. **IQ数据提取**: 支持不同数据格式(Complex8bit, Complex16bit, Complex32bit)

### 主要类和方法

- `IQSWavConverter`: 主转换器类
  - `get_iqs_wav_file_info()`: 获取WAV文件信息
  - `get_iqs_wav_file_data()`: 获取IQ数据
  - `convert_wav_to_csv()`: 转换单个文件
  - `batch_convert_directory()`: 批量转换目录

## 错误处理

- 文件不存在或无法读取时会记录错误并跳过
- 数据解析失败时会提供详细的错误信息
- 单个文件转换失败不会影响批量处理的继续进行

## 日志输出

程序会输出详细的处理日志，包括：
- 找到的WAV文件数量
- 每个文件的处理进度
- 转换成功/失败的信息
- 最终的转换统计

## 注意事项

1. 确保有足够的磁盘空间存储转换后的CSV文件
2. 大文件转换可能需要较长时间，请耐心等待
3. 确保Python环境已安装所需的依赖包
4. 建议在转换前备份重要的原始WAV文件

## 兼容性

- Python 3.6+
- Windows/Linux/macOS
- 与原始C++代码输出格式完全兼容
