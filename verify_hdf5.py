import h5py
import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('verify_hdf5.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def verify_hdf5(file_path):
    """
    Verify the contents of an HDF5 file created by new.py
    
    Args:
        file_path (str): Path to the HDF5 file
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"Error: File {file_path} does not exist.")
            sys.exit(1)
            
        logger.info(f"Verifying HDF5 file: {file_path}")
        
        with h5py.File(file_path, 'r') as f:
            # Get all class groups
            classes = list(f.keys())
            logger.info(f"Found {len(classes)} classes: {', '.join(classes)}")
            
            total_files = 0
            
            # Iterate through each class
            for class_name in classes:
                class_group = f[class_name]
                files = list(class_group.keys())
                logger.info(f"Class '{class_name}' contains {len(files)} files")
                total_files += len(files)
                
                # Check a sample file from each class
                if files:
                    sample_file = files[0]
                    file_group = class_group[sample_file]
                    
                    # Check if the required datasets and attributes exist
                    if 'I_data' in file_group and 'Q_data' in file_group:
                        i_data = file_group['I_data']
                        q_data = file_group['Q_data']
                        
                        logger.info(f"  Sample file '{sample_file}':")
                        logger.info(f"    I_data shape: {i_data.shape}")
                        logger.info(f"    Q_data shape: {q_data.shape}")
                        
                        # Check attributes
                        if 'CenterFreq_Hz' in file_group.attrs and 'IQSampleRate' in file_group.attrs:
                            logger.info(f"    CenterFreq_Hz: {file_group.attrs['CenterFreq_Hz']}")
                            logger.info(f"    IQSampleRate: {file_group.attrs['IQSampleRate']}")
                        else:
                            logger.warning(f"    Missing required attributes in file '{sample_file}'")
                        
                        # Check class attribute
                        if 'class' in file_group.attrs:
                            logger.info(f"    Class: {file_group.attrs['class']}")
                            
                            # Verify that the class attribute matches the group name
                            if file_group.attrs['class'] != class_name:
                                logger.warning(f"    Class attribute '{file_group.attrs['class']}' does not match group name '{class_name}'")
                        else:
                            logger.warning(f"    Missing 'class' attribute in file '{sample_file}'")
                    else:
                        logger.warning(f"  Sample file '{sample_file}' missing required datasets")
            
            logger.info(f"Total files in HDF5: {total_files}")
            
    except Exception as e:
        logger.error(f"Error verifying HDF5 file: {e}")
        sys.exit(1)

def main():
    """
    Main function to verify the HDF5 file
    """
    # File path
    hdf5_file = 'iq_data.h5'
    
    # Verify the HDF5 file
    verify_hdf5(hdf5_file)
    
    logger.info("Verification completed")

if __name__ == "__main__":
    main() 