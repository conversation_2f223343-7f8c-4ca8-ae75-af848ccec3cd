#!/usr/bin/env python3
"""
检查WAV文件是否包含GNSS信息的脚本
"""

import wave
import struct
import numpy as np
import os
import sys

def analyze_wav_file(filename):
    """分析WAV文件的结构和内容"""
    print(f"分析文件: {filename}")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    # 获取文件大小
    file_size = os.path.getsize(filename)
    print(f"文件大小: {file_size:,} 字节 ({file_size / (1024*1024):.2f} MB)")
    
    try:
        # 打开WAV文件
        with wave.open(filename, 'rb') as wav_file:
            # 获取WAV文件基本信息
            frames = wav_file.getnframes()
            sample_rate = wav_file.getframerate()
            channels = wav_file.getnchannels()
            sample_width = wav_file.getsampwidth()
            duration = frames / sample_rate
            
            print(f"\nWAV文件基本信息:")
            print(f"  声道数: {channels}")
            print(f"  采样率: {sample_rate} Hz")
            print(f"  采样位深: {sample_width * 8} bits")
            print(f"  总帧数: {frames:,}")
            print(f"  时长: {duration:.2f} 秒")
            
            # 读取一小部分数据进行分析
            sample_count = min(1000, frames)
            wav_file.setpos(0)
            raw_data = wav_file.readframes(sample_count)
            
            # 根据采样位深解析数据
            if sample_width == 1:
                # 8-bit unsigned
                samples = struct.unpack(f'{sample_count * channels}B', raw_data)
                samples = np.array(samples, dtype=np.float32) - 128
            elif sample_width == 2:
                # 16-bit signed
                samples = struct.unpack(f'{sample_count * channels}h', raw_data)
                samples = np.array(samples, dtype=np.float32)
            elif sample_width == 4:
                # 32-bit signed or float
                try:
                    samples = struct.unpack(f'{sample_count * channels}f', raw_data)
                    samples = np.array(samples, dtype=np.float32)
                except:
                    samples = struct.unpack(f'{sample_count * channels}i', raw_data)
                    samples = np.array(samples, dtype=np.float32)
            else:
                print(f"不支持的采样位深: {sample_width * 8} bits")
                return
            
            # 分析数据特征
            print(f"\n数据分析 (前{sample_count}个样本):")
            print(f"  最小值: {np.min(samples):.6f}")
            print(f"  最大值: {np.max(samples):.6f}")
            print(f"  平均值: {np.mean(samples):.6f}")
            print(f"  标准差: {np.std(samples):.6f}")
            
            # 检查是否为IQ数据
            if channels == 2:
                print(f"\n可能的IQ数据分析:")
                i_channel = samples[0::2]
                q_channel = samples[1::2]
                print(f"  I通道 - 平均值: {np.mean(i_channel):.6f}, 标准差: {np.std(i_channel):.6f}")
                print(f"  Q通道 - 平均值: {np.mean(q_channel):.6f}, 标准差: {np.std(q_channel):.6f}")
                
                # 计算复数幅度
                complex_samples = i_channel + 1j * q_channel
                magnitude = np.abs(complex_samples)
                phase = np.angle(complex_samples)
                print(f"  复数幅度 - 平均值: {np.mean(magnitude):.6f}, 标准差: {np.std(magnitude):.6f}")
                print(f"  相位 - 平均值: {np.mean(phase):.6f}, 标准差: {np.std(phase):.6f}")
            
    except Exception as e:
        print(f"读取WAV文件时出错: {e}")
        return
    
    # 检查文件名中的GNSS相关信息
    print(f"\n文件名分析:")
    filename_lower = filename.lower()
    gnss_keywords = ['gps', 'gnss', 'galileo', 'glonass', 'beidou', 'l1', 'l2', 'l5', 'e1', 'e5']
    found_keywords = [kw for kw in gnss_keywords if kw in filename_lower]
    if found_keywords:
        print(f"  发现GNSS相关关键词: {found_keywords}")
    else:
        print(f"  文件名中未发现明显的GNSS关键词")
    
    # 检查是否为IQ格式
    if 'iq' in filename_lower:
        print(f"  文件名包含'iq'，可能是IQ数据格式")
    
    # 分析采样率是否符合GNSS信号特征
    print(f"\nGNSS信号特征分析:")
    common_gnss_rates = [
        (2048000, "GPS L1 C/A"),
        (4092000, "GPS L1 C/A (2x)"),
        (8184000, "GPS L1 C/A (4x)"),
        (16368000, "GPS L1 C/A (8x)"),
        (1575420000, "GPS L1载波频率"),
        (10230000, "GPS L1 P(Y)"),
        (20460000, "GPS L1 P(Y) (2x)")
    ]
    
    for rate, description in common_gnss_rates:
        if abs(sample_rate - rate) / rate < 0.01:  # 1% tolerance
            print(f"  采样率 {sample_rate} Hz 接近 {description}")
    
    # 总结
    print(f"\n总结:")
    is_likely_gnss = False
    reasons = []
    
    if channels == 2:
        is_likely_gnss = True
        reasons.append("双声道可能表示IQ数据")
    
    if 'iq' in filename_lower:
        is_likely_gnss = True
        reasons.append("文件名包含IQ标识")
    
    if found_keywords:
        is_likely_gnss = True
        reasons.append(f"文件名包含GNSS关键词: {found_keywords}")
    
    if sample_rate >= 1000000:  # 1 MHz以上
        is_likely_gnss = True
        reasons.append("高采样率符合GNSS信号特征")
    
    if is_likely_gnss:
        print(f"  ✓ 该文件很可能包含GNSS信号数据")
        for reason in reasons:
            print(f"    - {reason}")
    else:
        print(f"  ✗ 该文件不太可能包含GNSS信号数据")
    
    return is_likely_gnss

def main():
    filename = "0053_20250723_202435.part1.iq.wav"
    analyze_wav_file(filename)

if __name__ == "__main__":
    main()
