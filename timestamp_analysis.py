#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间戳分析脚本
分析IQS WAV文件中的特殊时间戳格式
"""

from datetime import datetime, timezone
import struct

def analyze_timestamp(timestamp):
    """
    分析时间戳的可能格式

    Args:
        timestamp: 原始时间戳值
    """
    print(f"原始时间戳: {timestamp}")
    print(f"时间戳类型: {type(timestamp)}")
    print(f"时间戳长度: {len(str(int(timestamp)))} 位")

    # 文件名中的正确时间
    correct_time = datetime(2025, 7, 23, 20, 24, 35, tzinfo=timezone.utc)
    correct_timestamp = correct_time.timestamp()
    print(f"正确时间: {correct_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"正确时间的Unix时间戳: {correct_timestamp}")

    # 尝试不同的解释方式
    print("\n可能的时间戳格式分析:")
    print("-" * 50)
    
    # 1. 直接作为Unix时间戳（秒）
    try:
        dt1 = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        print(f"1. Unix时间戳（秒）: {dt1.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
    except (ValueError, OSError) as e:
        print(f"1. Unix时间戳（秒）: 失败 - {e}")
    
    # 2. 毫秒级时间戳
    try:
        dt2 = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc)
        print(f"2. 毫秒级时间戳: {dt2.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
    except (ValueError, OSError) as e:
        print(f"2. 毫秒级时间戳: 失败 - {e}")
    
    # 3. 微秒级时间戳
    try:
        dt3 = datetime.fromtimestamp(timestamp / 1000000, tz=timezone.utc)
        print(f"3. 微秒级时间戳: {dt3.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
    except (ValueError, OSError) as e:
        print(f"3. 微秒级时间戳: 失败 - {e}")
    
    # 4. 纳秒级时间戳
    try:
        dt4 = datetime.fromtimestamp(timestamp / 1000000000, tz=timezone.utc)
        print(f"4. 纳秒级时间戳: {dt4.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
    except (ValueError, OSError) as e:
        print(f"4. 纳秒级时间戳: 失败 - {e}")
    
    # 5. 100纳秒级时间戳（Windows FILETIME格式）
    try:
        # Windows FILETIME: 从1601年1月1日开始的100纳秒间隔数
        # 转换为Unix时间戳需要减去1601到1970的差值
        windows_epoch_diff = 11644473600  # 1601到1970的秒数
        dt5 = datetime.fromtimestamp((timestamp / 10000000) - windows_epoch_diff, tz=timezone.utc)
        print(f"5. Windows FILETIME: {dt5.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
    except (ValueError, OSError) as e:
        print(f"5. Windows FILETIME: 失败 - {e}")
    
    # 6. 系统特定的高精度计时器
    print(f"\n6. 可能的系统计时器分析:")
    print(f"   - 如果是系统启动后的计时器（毫秒）: {timestamp / 1000:.3f} 秒")
    print(f"   - 如果是系统启动后的计时器（微秒）: {timestamp / 1000000:.6f} 秒")
    print(f"   - 如果是系统启动后的计时器（纳秒）: {timestamp / 1000000000:.9f} 秒")
    
    # 7. 基于正确时间的反向分析
    print(f"\n7. 基于正确时间的反向分析:")
    print(f"   正确时间: 2025-07-23 20:24:35 UTC")
    print(f"   正确Unix时间戳: {correct_timestamp}")

    # 计算可能的转换因子
    ratio = timestamp / correct_timestamp
    print(f"   时间戳比值: {ratio}")
    print(f"   可能的转换因子:")
    print(f"     - 如果是纳秒: {timestamp / 1e9:.6f} -> {datetime.fromtimestamp(timestamp / 1e9, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S.%f UTC') if timestamp / 1e9 < 2**31 else '超出范围'}")
    print(f"     - 如果是微秒: {timestamp / 1e6:.6f} -> {datetime.fromtimestamp(timestamp / 1e6, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S.%f UTC') if timestamp / 1e6 < 2**31 else '超出范围'}")
    print(f"     - 如果是毫秒: {timestamp / 1e3:.6f} -> {datetime.fromtimestamp(timestamp / 1e3, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S.%f UTC') if timestamp / 1e3 < 2**31 else '超出范围'}")

    # 尝试找到正确的转换方法
    print(f"\n8. 寻找正确的转换方法:")

    # 方法1: 时间戳可能是从某个特定时间点开始的计数
    base_times = [
        ("Unix纪元 1970-01-01", datetime(1970, 1, 1, tzinfo=timezone.utc).timestamp()),
        ("Windows纪元 1601-01-01", -11644473600),  # Windows FILETIME基准
        ("GPS纪元 1980-01-06", datetime(1980, 1, 6, tzinfo=timezone.utc).timestamp()),
        ("设备可能的基准时间", datetime(2000, 1, 1, tzinfo=timezone.utc).timestamp()),
    ]

    for name, base_time in base_times:
        try:
            # 尝试不同的单位
            for unit_name, divisor in [("纳秒", 1e9), ("微秒", 1e6), ("毫秒", 1e3), ("秒", 1)]:
                calculated_time = base_time + (timestamp / divisor)
                if 0 < calculated_time < 2**31:  # 合理的时间戳范围
                    dt = datetime.fromtimestamp(calculated_time, tz=timezone.utc)
                    if abs(dt.timestamp() - correct_timestamp) < 86400:  # 在一天内
                        print(f"   ✓ 可能匹配: {name} + {timestamp}/{divisor:.0f} {unit_name} = {dt.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
        except:
            pass

def analyze_binary_representation(timestamp):
    """
    分析时间戳的二进制表示
    """
    print(f"\n二进制表示分析:")
    print("-" * 30)
    
    # 转换为整数
    timestamp_int = int(timestamp)
    
    # 二进制表示
    binary = bin(timestamp_int)
    print(f"二进制: {binary}")
    print(f"二进制长度: {len(binary) - 2} 位")  # 减去'0b'前缀
    
    # 十六进制表示
    hex_repr = hex(timestamp_int)
    print(f"十六进制: {hex_repr}")
    
    # 分析可能的字段
    print(f"\n可能的字段分解:")
    # 假设是64位时间戳，尝试分解
    if timestamp_int.bit_length() <= 64:
        # 高32位和低32位
        high_32 = (timestamp_int >> 32) & 0xFFFFFFFF
        low_32 = timestamp_int & 0xFFFFFFFF
        print(f"高32位: {high_32} (0x{high_32:08X})")
        print(f"低32位: {low_32} (0x{low_32:08X})")
        
        # 尝试将高32位作为秒，低32位作为分数秒
        try:
            dt_combined = datetime.fromtimestamp(high_32 + low_32 / (2**32), tz=timezone.utc)
            print(f"组合时间（高32位秒+低32位分数）: {dt_combined.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
        except:
            pass

def find_correct_timestamp_format(timestamp, target_time):
    """
    尝试找到正确的时间戳转换格式
    """
    print(f"\n" + "=" * 60)
    print("寻找正确的时间戳格式")
    print("=" * 60)

    target_timestamp = target_time.timestamp()
    print(f"目标时间: {target_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"目标Unix时间戳: {target_timestamp}")

    # 尝试各种可能的转换
    possible_formats = []

    # 1. 直接除法转换
    for unit_name, divisor in [("纳秒", 1e9), ("微秒", 1e6), ("毫秒", 1e3), ("秒", 1)]:
        try:
            converted = timestamp / divisor
            if 0 < converted < 2**31:
                dt = datetime.fromtimestamp(converted, tz=timezone.utc)
                diff = abs(dt.timestamp() - target_timestamp)
                if diff < 86400:  # 在一天内
                    possible_formats.append((f"直接转换/{unit_name}", dt, diff))
        except:
            pass

    # 2. 加上基准时间
    base_times = [
        ("Unix纪元", 0),
        ("2000年", datetime(2000, 1, 1, tzinfo=timezone.utc).timestamp()),
        ("GPS纪元", datetime(1980, 1, 6, tzinfo=timezone.utc).timestamp()),
    ]

    for base_name, base_time in base_times:
        for unit_name, divisor in [("纳秒", 1e9), ("微秒", 1e6), ("毫秒", 1e3), ("秒", 1)]:
            try:
                converted = base_time + (timestamp / divisor)
                if 0 < converted < 2**31:
                    dt = datetime.fromtimestamp(converted, tz=timezone.utc)
                    diff = abs(dt.timestamp() - target_timestamp)
                    if diff < 86400:
                        possible_formats.append((f"{base_name}+{unit_name}", dt, diff))
            except:
                pass

    # 排序并显示最佳匹配
    possible_formats.sort(key=lambda x: x[2])

    print(f"\n可能的正确格式（按匹配度排序）:")
    for i, (format_name, dt, diff) in enumerate(possible_formats[:5]):
        print(f"{i+1}. {format_name}: {dt.strftime('%Y-%m-%d %H:%M:%S.%f UTC')} (误差: {diff:.2f}秒)")

    return possible_formats[0] if possible_formats else None

def main():
    """主函数"""
    print("=" * 60)
    print("时间戳格式分析 - 基于正确时间")
    print("=" * 60)

    # 从GNSS数据中获取时间戳
    timestamp = 990956329492035.0

    # 正确的时间（来自文件名）
    correct_time = datetime(2025, 7, 23, 20, 24, 35, tzinfo=timezone.utc)

    analyze_timestamp(timestamp)
    analyze_binary_representation(timestamp)

    # 寻找正确的转换格式
    best_format = find_correct_timestamp_format(timestamp, correct_time)

    print(f"\n" + "=" * 60)
    print("结论和建议:")
    print("=" * 60)
    if best_format:
        print(f"✓ 找到最佳匹配格式: {best_format[0]}")
        print(f"✓ 转换结果: {best_format[1].strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
        print(f"✓ 与目标时间误差: {best_format[2]:.2f}秒")
    else:
        print("❌ 未找到合适的转换格式")
        print("建议：")
        print("1. 检查设备文档中的时间戳格式说明")
        print("2. 时间戳可能使用了特殊的基准时间或编码方式")
        print("3. 可能需要额外的校准参数")

if __name__ == "__main__":
    main()
