#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通用化时间戳解析功能
"""

from datetime import datetime, timezone, timedelta

def convert_timestamp_to_datetime(timestamp_value):
    """
    通用化的时间戳转换函数（从main.py复制）
    """
    try:
        timestamp_int = int(timestamp_value)
        high_32 = (timestamp_int >> 32) & 0xFFFFFFFF
        low_32 = timestamp_int & 0xFFFFFFFF
        
        # 解析日期部分 (高32位)
        # 格式：DDMMYY (例如：230725 -> 2025-07-23)
        high_str = str(high_32).zfill(6)
        day = int(high_str[0:2])
        month = int(high_str[2:4])
        year = 2000 + int(high_str[4:6])
        
        # 验证日期的合理性
        if not (1 <= day <= 31 and 1 <= month <= 12 and 2000 <= year <= 2100):
            raise ValueError(f"无效的日期: {year}-{month:02d}-{day:02d}")
        
        # 解析时间部分 (低32位)
        # 格式：HHMMSS，这里的时间是UTC时间
        # 例如：122435 -> 12:24:35 UTC
        low_str = str(low_32).zfill(6)
        hour_utc = int(low_str[0:2])
        minute = int(low_str[2:4])
        second = int(low_str[4:6])
        
        # 验证时间的合理性
        if not (0 <= hour_utc <= 23 and 0 <= minute <= 59 and 0 <= second <= 59):
            raise ValueError(f"无效的时间: {hour_utc:02d}:{minute:02d}:{second:02d}")
        
        # 先创建UTC时间
        dt_utc = datetime(year, month, day, hour_utc, minute, second, tzinfo=timezone.utc)
        
        # 转换为北京时间（UTC+8）
        beijing_tz = timezone(timedelta(hours=8))
        dt_beijing = dt_utc.astimezone(beijing_tz)
        
        return {
            'utc_time': dt_utc.strftime('%Y-%m-%d %H:%M:%S.%f UTC'),
            'beijing_time': dt_beijing.strftime('%Y-%m-%d %H:%M:%S.%f CST'),
            'unix_timestamp': dt_utc.timestamp()
        }
    except Exception as e:
        return {
            'utc_time': f'转换失败: {e}',
            'beijing_time': f'转换失败: {e}',
            'unix_timestamp': 0
        }

def create_test_timestamp(date_str, time_str):
    """
    根据日期时间字符串创建测试用的时间戳
    
    Args:
        date_str: 日期字符串，格式 "YYYY-MM-DD"
        time_str: 时间字符串，格式 "HH:MM:SS" (UTC时间)
    
    Returns:
        模拟的时间戳值
    """
    # 解析日期
    year, month, day = map(int, date_str.split('-'))
    
    # 解析时间
    hour, minute, second = map(int, time_str.split(':'))
    
    # 创建高32位：DDMMYY格式
    yy = year % 100
    high_32 = day * 10000 + month * 100 + yy
    
    # 创建低32位：HHMMSS格式
    low_32 = hour * 10000 + minute * 100 + second
    
    # 组合为64位时间戳
    timestamp = (high_32 << 32) | low_32
    
    return float(timestamp)

def test_timestamp_conversion():
    """
    测试时间戳转换功能
    """
    print("=" * 80)
    print("测试通用化时间戳解析功能")
    print("=" * 80)
    
    # 测试用例
    test_cases = [
        # (日期, UTC时间, 期望的北京时间)
        ("2025-07-23", "12:24:35", "2025-07-23 20:24:35"),  # 原始测试用例
        ("2024-12-31", "16:00:00", "2025-01-01 00:00:00"),  # 跨年测试
        ("2025-01-01", "00:00:00", "2025-01-01 08:00:00"),  # 新年测试
        ("2025-06-15", "06:30:45", "2025-06-15 14:30:45"),  # 普通日期测试
        ("2025-02-28", "23:59:59", "2025-03-01 07:59:59"),  # 跨日测试
    ]
    
    print(f"{'测试用例':<20} {'输入UTC时间':<20} {'期望北京时间':<20} {'实际北京时间':<20} {'结果':<10}")
    print("-" * 100)
    
    for i, (date_str, utc_time, expected_beijing) in enumerate(test_cases, 1):
        # 创建测试时间戳
        test_timestamp = create_test_timestamp(date_str, utc_time)
        
        # 转换时间戳
        result = convert_timestamp_to_datetime(test_timestamp)
        
        # 提取实际的北京时间（去掉微秒和时区信息）
        actual_beijing = result['beijing_time'].split('.')[0].replace(' CST', '')
        
        # 检查结果
        success = actual_beijing == expected_beijing
        status = "✅ 通过" if success else "❌ 失败"
        
        print(f"测试{i:<15} {date_str} {utc_time:<12} {expected_beijing:<20} {actual_beijing:<20} {status}")
        
        if not success:
            print(f"  详细信息:")
            print(f"    时间戳值: {test_timestamp}")
            print(f"    高32位: {(int(test_timestamp) >> 32) & 0xFFFFFFFF}")
            print(f"    低32位: {int(test_timestamp) & 0xFFFFFFFF}")
            print(f"    UTC时间: {result['utc_time']}")
            print(f"    北京时间: {result['beijing_time']}")
    
    print("\n" + "=" * 80)
    print("测试原始时间戳值")
    print("=" * 80)
    
    # 测试原始的时间戳值
    original_timestamp = 990956329492035.0
    result = convert_timestamp_to_datetime(original_timestamp)
    
    print(f"原始时间戳: {original_timestamp}")
    print(f"UTC时间: {result['utc_time']}")
    print(f"北京时间: {result['beijing_time']}")
    print(f"Unix时间戳: {result['unix_timestamp']}")
    
    # 验证是否与期望一致
    expected_beijing_original = "2025-07-23 20:24:35"
    actual_beijing_original = result['beijing_time'].split('.')[0].replace(' CST', '')
    
    if actual_beijing_original == expected_beijing_original:
        print("✅ 原始时间戳解析正确!")
    else:
        print(f"❌ 原始时间戳解析错误! 期望: {expected_beijing_original}, 实际: {actual_beijing_original}")

def demonstrate_usage():
    """
    演示如何使用通用化的时间戳解析
    """
    print("\n" + "=" * 80)
    print("使用示例")
    print("=" * 80)
    
    print("1. 如果您有新的WAV文件，时间戳解析将自动适应")
    print("2. 时间戳格式：64位，高32位=DDMMYY，低32位=HHMMSS(UTC)")
    print("3. 自动转换为UTC和北京时间")
    print("4. 包含日期时间验证，防止无效数据")
    
    print("\n示例代码:")
    print("""
# 在main.py中使用
gnss_data = extract_gnss_info("your_new_file.wav")
for gnss in gnss_data:
    print(f"UTC时间: {gnss['UTC_Time']}")
    print(f"北京时间: {gnss['Beijing_Time']}")
    print(f"位置: {gnss['Latitude']}, {gnss['Longitude']}")
""")

def main():
    """主函数"""
    test_timestamp_conversion()
    demonstrate_usage()

if __name__ == "__main__":
    main()
