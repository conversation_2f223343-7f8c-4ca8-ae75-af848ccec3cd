#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时区时间戳分析脚本
考虑时区因素的时间戳分析
"""

from datetime import datetime, timezone, timedelta
import pytz

def analyze_with_timezone(timestamp_value, target_local_time="2025-07-23 20:24:35"):
    """
    考虑时区的时间戳分析
    """
    print(f"原始时间戳: {timestamp_value}")
    print(f"目标本地时间: {target_local_time}")
    print("-" * 80)
    
    # 解析目标本地时间
    target_dt_naive = datetime.strptime(target_local_time, "%Y-%m-%d %H:%M:%S")
    
    # 常见时区
    common_timezones = [
        ('UTC', timezone.utc),
        ('北京时间 (UTC+8)', timezone(timedelta(hours=8))),
        ('东京时间 (UTC+9)', timezone(timedelta(hours=9))),
        ('首尔时间 (UTC+9)', timezone(timedelta(hours=9))),
        ('台北时间 (UTC+8)', timezone(timedelta(hours=8))),
        ('香港时间 (UTC+8)', timezone(timedelta(hours=8))),
        ('新加坡时间 (UTC+8)', timezone(timedelta(hours=8))),
        ('美国东部时间 (UTC-5)', timezone(timedelta(hours=-5))),
        ('美国西部时间 (UTC-8)', timezone(timedelta(hours=-8))),
        ('欧洲中部时间 (UTC+1)', timezone(timedelta(hours=1))),
    ]
    
    print("尝试不同时区的解释:")
    print("-" * 80)
    
    best_matches = []
    
    for tz_name, tz in common_timezones:
        # 将目标时间解释为该时区的本地时间
        target_dt_tz = target_dt_naive.replace(tzinfo=tz)
        target_utc_timestamp = target_dt_tz.timestamp()
        
        print(f"\n{tz_name}:")
        print(f"  本地时间: {target_dt_tz.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        print(f"  UTC时间: {target_dt_tz.astimezone(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print(f"  Unix时间戳: {target_utc_timestamp}")
        
        # 尝试不同的时间戳单位
        time_units = [
            ("微秒", 1000000),
            ("纳秒", 1000000000),
            ("100纳秒", 10000000),
        ]
        
        for unit_name, divisor in time_units:
            try:
                # 计算从Unix epoch开始的时间
                calculated_utc_timestamp = timestamp_value / divisor
                calculated_dt_utc = datetime.fromtimestamp(calculated_utc_timestamp, tz=timezone.utc)
                calculated_dt_local = calculated_dt_utc.astimezone(tz)
                
                # 计算与目标时间的差异
                time_diff = abs(calculated_utc_timestamp - target_utc_timestamp)
                
                print(f"    {unit_name:8}: UTC {calculated_dt_utc.strftime('%Y-%m-%d %H:%M:%S.%f')}")
                print(f"             本地 {calculated_dt_local.strftime('%Y-%m-%d %H:%M:%S.%f %Z')}")
                print(f"             差异: {time_diff:.2f}秒")
                
                if time_diff < 3600:  # 差异小于1小时
                    match = {
                        'timezone': tz_name,
                        'unit': unit_name,
                        'utc_time': calculated_dt_utc,
                        'local_time': calculated_dt_local,
                        'time_diff': time_diff
                    }
                    best_matches.append(match)
                    print(f"             *** 这是一个很好的匹配! ***")
                
            except (ValueError, OSError, OverflowError):
                print(f"    {unit_name:8}: 计算失败")
    
    # 特别检查从之前分析中发现的epoch
    print(f"\n特别检查发现的epoch:")
    print("-" * 80)
    
    # 从之前的分析中，我们发现微秒格式给出了2025-07-23 12:24:35 UTC
    # 这意味着如果目标时间是北京时间20:24:35，那么UTC时间应该是12:24:35
    
    # 验证：如果目标时间是北京时间，UTC时间应该是12:24:35
    beijing_tz = timezone(timedelta(hours=8))
    target_beijing = target_dt_naive.replace(tzinfo=beijing_tz)
    target_utc_from_beijing = target_beijing.astimezone(timezone.utc)
    
    print(f"如果目标时间是北京时间:")
    print(f"  北京时间: {target_beijing.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"  对应UTC: {target_utc_from_beijing.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    
    # 使用微秒格式计算
    calculated_utc_timestamp = timestamp_value / 1000000
    calculated_dt_utc = datetime.fromtimestamp(calculated_utc_timestamp, tz=timezone.utc)
    calculated_beijing = calculated_dt_utc.astimezone(beijing_tz)
    
    print(f"  时间戳转换结果:")
    print(f"    UTC: {calculated_dt_utc.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
    print(f"    北京时间: {calculated_beijing.strftime('%Y-%m-%d %H:%M:%S.%f %Z')}")
    
    utc_diff = abs(calculated_dt_utc.timestamp() - target_utc_from_beijing.timestamp())
    beijing_diff = abs(calculated_beijing.timestamp() - target_beijing.timestamp())
    
    print(f"  UTC时间差异: {utc_diff:.2f}秒")
    print(f"  北京时间差异: {beijing_diff:.2f}秒")
    
    if utc_diff < 60:
        print(f"  *** 找到精确匹配! 时间戳是微秒格式的UTC时间 ***")
        best_matches.insert(0, {
            'timezone': '北京时间解释',
            'unit': '微秒',
            'utc_time': calculated_dt_utc,
            'local_time': calculated_beijing,
            'time_diff': utc_diff
        })
    
    return best_matches

def main():
    """主函数"""
    print("=" * 80)
    print("时区时间戳分析")
    print("=" * 80)
    
    # 使用之前获取的时间戳值
    timestamp_value = 990956329492035.0
    
    best_matches = analyze_with_timezone(timestamp_value)
    
    if best_matches:
        print("\n" + "=" * 80)
        print("最佳匹配结果:")
        print("=" * 80)
        
        best = best_matches[0]
        print(f"时区解释: {best['timezone']}")
        print(f"时间戳单位: {best['unit']}")
        print(f"UTC时间: {best['utc_time'].strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
        print(f"本地时间: {best['local_time'].strftime('%Y-%m-%d %H:%M:%S.%f %Z')}")
        print(f"时间差异: {best['time_diff']:.2f}秒")
        
        print(f"\n结论:")
        print(f"时间戳 {timestamp_value} 表示:")
        print(f"- 格式: 微秒级Unix时间戳")
        print(f"- UTC时间: {best['utc_time'].strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
        print(f"- 如果设备在中国，本地时间: {best['local_time'].strftime('%Y-%m-%d %H:%M:%S %Z')}")
    else:
        print("\n" + "=" * 80)
        print("未找到匹配的时区解释")
        print("=" * 80)

if __name__ == "__main__":
    main()
