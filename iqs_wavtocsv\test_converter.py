#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IQS WAV转换器测试脚本
"""

import os
import sys
from pathlib import Path
from iqs_wav_to_csv import IQSWavConverter
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_single_file_conversion():
    """测试单个文件转换"""
    print("=" * 50)
    print("测试单个文件转换")
    print("=" * 50)
    
    # 这里使用你提供的文件路径作为示例
    test_file = r"D:\nxm-60\Windows\SAStudio4\data\BPSKQ2\0053_20250630_232013.part1.iq.wav"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        print("请修改test_file变量为实际存在的WAV文件路径")
        return False
    
    try:
        converter = IQSWavConverter()
        csv_file = converter.convert_wav_to_csv(test_file)
        print(f"✓ 单个文件转换成功: {csv_file}")
        return True
    except Exception as e:
        print(f"✗ 单个文件转换失败: {e}")
        return False

def test_batch_conversion():
    """测试批量转换"""
    print("\n" + "=" * 50)
    print("测试批量转换")
    print("=" * 50)
    
    # 测试目录
    test_dir = r"D:\nxm-60\Windows\SAStudio4\data"
    
    if not os.path.exists(test_dir):
        print(f"测试目录不存在: {test_dir}")
        print("请修改test_dir变量为实际存在的目录路径")
        return False
    
    try:
        converter = IQSWavConverter()
        converted_files = converter.batch_convert_directory(test_dir)
        print(f"✓ 批量转换成功，共转换 {len(converted_files)} 个文件")
        
        if converted_files:
            print("\n转换的文件列表:")
            for i, file_path in enumerate(converted_files, 1):
                print(f"  {i}. {file_path}")
        
        return True
    except Exception as e:
        print(f"✗ 批量转换失败: {e}")
        return False

def test_file_info_reading():
    """测试文件信息读取"""
    print("\n" + "=" * 50)
    print("测试文件信息读取")
    print("=" * 50)
    
    test_file = r"D:\nxm-60\Windows\SAStudio4\data\BPSKQ2\0053_20250630_232013.part1.iq.wav"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return False
    
    try:
        converter = IQSWavConverter()
        iqs_profile, iqs_stream_info, device_info, packet_count = converter.get_iqs_wav_file_info(test_file)
        
        print("✓ 文件信息读取成功")
        print(f"\n设备信息:")
        print(f"  Device UID: {device_info.get('DeviceUID', 'N/A'):x}")
        print(f"  Model: {device_info.get('Model', 'N/A')}")
        print(f"  Hardware Version: {device_info.get('HardwareVersion', 'N/A')}")
        
        print(f"\nIQS Profile:")
        print(f"  Center Frequency: {iqs_profile.get('CenterFreq_Hz', 'N/A')} Hz")
        print(f"  Reference Level: {iqs_profile.get('RefLevel_dBm', 'N/A')} dBm")
        print(f"  Decimate Factor: {iqs_profile.get('DecimateFactor', 'N/A')}")
        print(f"  Data Format: {iqs_profile.get('DataFormat', 'N/A')}")
        
        print(f"\nStream Info:")
        print(f"  Bandwidth: {iqs_stream_info.get('Bandwidth', 'N/A')}")
        print(f"  IQ Sample Rate: {iqs_stream_info.get('IQSampleRate', 'N/A')}")
        print(f"  Packet Count: {packet_count}")
        print(f"  Stream Samples: {iqs_stream_info.get('StreamSamples', 'N/A')}")
        print(f"  Packet Samples: {iqs_stream_info.get('PacketSamples', 'N/A')}")
        
        return True
    except Exception as e:
        print(f"✗ 文件信息读取失败: {e}")
        return False

def test_directory_scanning():
    """测试目录扫描功能"""
    print("\n" + "=" * 50)
    print("测试目录扫描功能")
    print("=" * 50)
    
    test_dir = r"D:\nxm-60\Windows\SAStudio4\data"
    
    if not os.path.exists(test_dir):
        print(f"测试目录不存在: {test_dir}")
        return False
    
    try:
        root_path = Path(test_dir)
        wav_files = list(root_path.rglob("*.wav"))
        
        print(f"✓ 目录扫描成功")
        print(f"找到 {len(wav_files)} 个WAV文件:")
        
        for i, wav_file in enumerate(wav_files, 1):
            rel_path = wav_file.relative_to(root_path)
            file_size = wav_file.stat().st_size / (1024 * 1024)  # MB
            print(f"  {i}. {rel_path} ({file_size:.2f} MB)")
        
        return True
    except Exception as e:
        print(f"✗ 目录扫描失败: {e}")
        return False

def main():
    """主测试函数"""
    print("IQS WAV转换器测试程序")
    print("=" * 50)
    
    # 检查依赖
    try:
        import msgpack
        import pandas as pd
        print("✓ 依赖包检查通过")
    except ImportError as e:
        print(f"✗ 缺少依赖包: {e}")
        print("请运行: pip install -r requirements.txt")
        return
    
    # 运行测试
    tests = [
        ("目录扫描", test_directory_scanning),
        ("文件信息读取", test_file_info_reading),
        ("单个文件转换", test_single_file_conversion),
        ("批量转换", test_batch_conversion),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！转换器可以正常使用。")
    else:
        print("⚠️  部分测试失败，请检查配置和文件路径。")

if __name__ == "__main__":
    main()
