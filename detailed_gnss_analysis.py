#!/usr/bin/env python3
"""
详细的GNSS信号分析脚本
"""

import wave
import struct
import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import os

def detailed_gnss_analysis(filename):
    """详细分析WAV文件中的GNSS信号特征"""
    print(f"详细GNSS信号分析: {filename}")
    print("=" * 60)
    
    try:
        with wave.open(filename, 'rb') as wav_file:
            # 获取基本信息
            frames = wav_file.getnframes()
            sample_rate = wav_file.getframerate()
            channels = wav_file.getnchannels()
            sample_width = wav_file.getsampwidth()
            
            print(f"采样率: {sample_rate:,} Hz")
            print(f"声道数: {channels}")
            print(f"总帧数: {frames:,}")
            
            # 读取更多数据进行分析
            sample_count = min(100000, frames)  # 读取更多样本
            wav_file.setpos(0)
            raw_data = wav_file.readframes(sample_count)
            
            # 解析16-bit数据
            samples = struct.unpack(f'{sample_count * channels}h', raw_data)
            samples = np.array(samples, dtype=np.float32)
            
            if channels == 2:
                # 分离I和Q通道
                i_channel = samples[0::2]
                q_channel = samples[1::2]
                
                print(f"\nIQ数据统计:")
                print(f"I通道: 均值={np.mean(i_channel):.2f}, 标准差={np.std(i_channel):.2f}")
                print(f"Q通道: 均值={np.mean(q_channel):.2f}, 标准差={np.std(q_channel):.2f}")
                
                # 创建复数信号
                complex_signal = i_channel + 1j * q_channel
                
                # 计算功率谱密度
                print(f"\n计算功率谱密度...")
                freqs, psd = signal.welch(complex_signal, fs=sample_rate, nperseg=1024)
                
                # 寻找峰值
                peak_indices = signal.find_peaks(psd, height=np.max(psd)*0.1)[0]
                peak_freqs = freqs[peak_indices]
                peak_powers = psd[peak_indices]
                
                print(f"发现 {len(peak_indices)} 个显著频率峰值:")
                for i, (freq, power) in enumerate(zip(peak_freqs, peak_powers)):
                    print(f"  峰值 {i+1}: {freq:.0f} Hz, 功率: {10*np.log10(power):.1f} dB")
                
                # 检查是否有GPS L1 C/A码的特征
                # GPS L1 C/A码的码片速率是1.023 MHz
                gps_ca_rate = 1023000  # Hz
                
                print(f"\nGPS L1 C/A码特征检查:")
                print(f"GPS C/A码片速率: {gps_ca_rate:,} Hz")
                
                # 检查采样率与GPS信号的关系
                if sample_rate % gps_ca_rate == 0:
                    oversampling = sample_rate // gps_ca_rate
                    print(f"采样率是GPS C/A码片速率的 {oversampling} 倍")
                else:
                    ratio = sample_rate / gps_ca_rate
                    print(f"采样率与GPS C/A码片速率的比值: {ratio:.2f}")
                
                # 分析信号的自相关特性
                print(f"\n自相关分析...")
                autocorr = np.correlate(complex_signal[:10000], complex_signal[:10000], mode='full')
                autocorr = autocorr[autocorr.size // 2:]
                
                # 寻找周期性
                autocorr_peaks = signal.find_peaks(np.abs(autocorr[1:]), height=np.max(np.abs(autocorr))*0.1)[0]
                if len(autocorr_peaks) > 0:
                    period_samples = autocorr_peaks[0] + 1
                    period_time = period_samples / sample_rate
                    period_freq = 1 / period_time
                    print(f"检测到周期性: {period_samples} 样本, {period_time*1000:.3f} ms, {period_freq:.0f} Hz")
                
                # 检查信号强度分布
                magnitude = np.abs(complex_signal)
                print(f"\n信号幅度分析:")
                print(f"最小幅度: {np.min(magnitude):.2f}")
                print(f"最大幅度: {np.max(magnitude):.2f}")
                print(f"平均幅度: {np.mean(magnitude):.2f}")
                print(f"幅度标准差: {np.std(magnitude):.2f}")
                
                # 检查相位分布
                phase = np.angle(complex_signal)
                print(f"\n相位分析:")
                print(f"相位范围: {np.min(phase):.3f} 到 {np.max(phase):.3f} 弧度")
                print(f"平均相位: {np.mean(phase):.3f} 弧度")
                print(f"相位标准差: {np.std(phase):.3f} 弧度")
                
                # 生成简单的可视化图表
                try:
                    plt.figure(figsize=(15, 10))
                    
                    # 时域信号
                    plt.subplot(2, 3, 1)
                    time_axis = np.arange(min(1000, len(i_channel))) / sample_rate * 1000
                    plt.plot(time_axis, i_channel[:len(time_axis)], 'b-', label='I')
                    plt.plot(time_axis, q_channel[:len(time_axis)], 'r-', label='Q')
                    plt.xlabel('时间 (ms)')
                    plt.ylabel('幅度')
                    plt.title('IQ时域信号')
                    plt.legend()
                    plt.grid(True)
                    
                    # 星座图
                    plt.subplot(2, 3, 2)
                    sample_step = max(1, len(i_channel) // 1000)
                    plt.scatter(i_channel[::sample_step], q_channel[::sample_step], alpha=0.5, s=1)
                    plt.xlabel('I')
                    plt.ylabel('Q')
                    plt.title('星座图')
                    plt.grid(True)
                    plt.axis('equal')
                    
                    # 功率谱
                    plt.subplot(2, 3, 3)
                    plt.semilogy(freqs/1e6, psd)
                    plt.xlabel('频率 (MHz)')
                    plt.ylabel('功率谱密度')
                    plt.title('功率谱密度')
                    plt.grid(True)
                    
                    # 幅度直方图
                    plt.subplot(2, 3, 4)
                    plt.hist(magnitude, bins=50, alpha=0.7)
                    plt.xlabel('幅度')
                    plt.ylabel('计数')
                    plt.title('幅度分布')
                    plt.grid(True)
                    
                    # 相位直方图
                    plt.subplot(2, 3, 5)
                    plt.hist(phase, bins=50, alpha=0.7)
                    plt.xlabel('相位 (弧度)')
                    plt.ylabel('计数')
                    plt.title('相位分布')
                    plt.grid(True)
                    
                    # 自相关
                    plt.subplot(2, 3, 6)
                    lag_axis = np.arange(min(1000, len(autocorr))) / sample_rate * 1000
                    plt.plot(lag_axis, np.abs(autocorr[:len(lag_axis)]))
                    plt.xlabel('延迟 (ms)')
                    plt.ylabel('自相关幅度')
                    plt.title('自相关函数')
                    plt.grid(True)
                    
                    plt.tight_layout()
                    plt.savefig('gnss_signal_analysis.png', dpi=150, bbox_inches='tight')
                    print(f"\n分析图表已保存为: gnss_signal_analysis.png")
                    
                except Exception as e:
                    print(f"生成图表时出错: {e}")
                
                # 最终判断
                print(f"\n" + "="*60)
                print(f"GNSS信号判断结果:")
                
                gnss_indicators = []
                confidence_score = 0
                
                # 检查各项指标
                if channels == 2:
                    gnss_indicators.append("✓ 双声道IQ数据格式")
                    confidence_score += 25
                
                if 'iq' in filename.lower():
                    gnss_indicators.append("✓ 文件名包含IQ标识")
                    confidence_score += 15
                
                if sample_rate >= 1000000:
                    gnss_indicators.append("✓ 高采样率 (>1MHz)")
                    confidence_score += 20
                
                if sample_rate % gps_ca_rate == 0:
                    gnss_indicators.append("✓ 采样率是GPS C/A码片速率的整数倍")
                    confidence_score += 25
                
                if np.std(magnitude) / np.mean(magnitude) < 0.5:
                    gnss_indicators.append("✓ 信号幅度相对稳定")
                    confidence_score += 10
                
                if len(peak_indices) > 0:
                    gnss_indicators.append("✓ 检测到频域峰值")
                    confidence_score += 5
                
                print(f"置信度评分: {confidence_score}/100")
                print(f"GNSS信号指标:")
                for indicator in gnss_indicators:
                    print(f"  {indicator}")
                
                if confidence_score >= 70:
                    print(f"\n结论: 该文件很可能包含GNSS信号数据 (置信度: {confidence_score}%)")
                elif confidence_score >= 40:
                    print(f"\n结论: 该文件可能包含GNSS信号数据 (置信度: {confidence_score}%)")
                else:
                    print(f"\n结论: 该文件不太可能包含GNSS信号数据 (置信度: {confidence_score}%)")
                
    except Exception as e:
        print(f"分析过程中出错: {e}")

def main():
    filename = "0053_20250723_202435.part1.iq.wav"
    detailed_gnss_analysis(filename)

if __name__ == "__main__":
    main()
