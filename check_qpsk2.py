import h5py

# Open the HDF5 file
with h5py.File('iq_data.h5', 'r') as f:
    # Check if QPSK2 exists
    if 'QPSK2' in f:
        print("QPSK2 group exists")
        
        # Get QPSK2 group
        qpsk2 = f['QPSK2']
        
        # List all subgroups
        subgroups = list(qpsk2.keys())
        print(f"QPSK2 subgroups: {subgroups}")
        
        # Check the first subgroup
        if subgroups:
            first_sg = subgroups[0]
            print(f"First subgroup '{first_sg}' contains: {list(qpsk2[first_sg].keys())}")
            
            # Check I_data and Q_data
            if 'I_data' in qpsk2[first_sg]:
                i_data = qpsk2[first_sg]['I_data']
                print(f"I_data shape: {i_data.shape}, dtype: {i_data.dtype}")
                print(f"I_data first 5 elements: {i_data[:5]}")
            
            if 'Q_data' in qpsk2[first_sg]:
                q_data = qpsk2[first_sg]['Q_data']
                print(f"Q_data shape: {q_data.shape}, dtype: {q_data.dtype}")
                print(f"Q_data first 5 elements: {q_data[:5]}")
        
        # Count total data points
        total_i = 0
        total_q = 0
        for sg in subgroups:
            if 'I_data' in qpsk2[sg]:
                total_i += qpsk2[sg]['I_data'].size
            if 'Q_data' in qpsk2[sg]:
                total_q += qpsk2[sg]['Q_data'].size
        
        print(f"Total I_data points: {total_i}")
        print(f"Total Q_data points: {total_q}")
        print(f"Total data points: {total_i + total_q}")
    else:
        print("QPSK2 group does not exist") 