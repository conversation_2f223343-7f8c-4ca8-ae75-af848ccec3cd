# 整合的IQ数据分析和地图生成器

## 概述

这是一个整合了 `main.py` 和 `map.py` 功能的综合分析工具，能够：

1. **读取WAV文件**：提取IQ数据和GNSS坐标信息
2. **生成时频图**：使用短时傅里叶变换(STFT)分析IQ数据的频谱特性
3. **生成地图**：获取高德地图卫星图像并标记GNSS坐标点
4. **图像合成**：将地图和时频图合成为一张综合分析图

## 主要功能

### 🔍 IQ数据分析
- 从WAV文件中提取复数IQ数据
- 使用短时傅里叶变换生成时频图
- 支持复数信号的频谱分析
- 自动处理字节序转换

### 🗺️ 地图生成
- 提取GNSS坐标信息（经纬度）
- WGS-84到GCJ-02坐标系转换
- 获取高德地图卫星图像
- 在地图上标记精确位置

### 🖼️ 图像合成
- 将地图和时频图合成为一张图
- 在地图右上角显示时频图
- 添加坐标和文件信息标注
- 支持自定义输出文件名

## 使用方法

### 1. 交互式模式

```bash
python integrated_iq_map_analyzer.py
```

程序会：
1. 自动扫描当前目录中的WAV文件
2. 让用户选择要分析的文件
3. 自动提取IQ数据和GNSS信息
4. 生成时频图和地图
5. 合成最终的分析图像

### 2. 命令行模式

```bash
python integrated_iq_map_analyzer.py <WAV文件路径> [输出文件名]
```

参数说明：
- `WAV文件路径`: 必需，要分析的WAV文件
- `输出文件名`: 可选，输出图片文件名

示例：
```bash
# 使用默认输出文件名
python integrated_iq_map_analyzer.py 0053_20250725_083011.part1.iq.wav

# 指定输出文件名
python integrated_iq_map_analyzer.py 0053_20250725_083011.part1.iq.wav my_analysis.png
```

## 输出结果

### 生成的图像包含：

1. **主地图区域**：
   - 高德卫星地图
   - 红色标记点显示GNSS坐标位置

2. **时频图区域**（右上角）：
   - IQ数据的短时傅里叶变换结果
   - 频率-时间-功率的三维可视化
   - 彩色编码显示信号强度

3. **信息标注**（底部）：
   - 精确的经纬度坐标
   - WAV文件名信息

### 文件命名规则：
- 交互模式：`integrated_analysis_<WAV文件名>.png`
- 命令行模式：用户指定或默认命名

## 技术特性

### 信号处理
- **STFT参数**：
  - 窗函数：Hann窗
  - 窗长度：256个样本
  - 重叠：128个样本
  - 支持复数信号分析

### 地图功能
- **地图参数**：
  - 尺寸：1200x800像素
  - 缩放级别：16（可调整）
  - 地图类型：卫星图
  - 标记：红色圆点

### 图像合成
- **合成布局**：
  - 主画布：地图尺寸+边距
  - 时频图：300x200像素（可调整）
  - 位置：右上角
  - 背景：白色边框

## 依赖库

```python
import requests          # HTTP请求
import numpy as np       # 数值计算
import matplotlib.pyplot as plt  # 绘图
from PIL import Image, ImageDraw, ImageFont  # 图像处理
from scipy import signal  # 信号处理
import msgpack          # 数据解析
```

## 错误处理

- **文件不存在**：提示错误并退出
- **GNSS数据缺失**：使用默认坐标继续处理
- **网络错误**：显示详细错误信息
- **图像处理失败**：返回原始地图作为备用
- **字体缺失**：自动使用默认字体

## 性能优化

- **数据限制**：每个数据包最多读取16000个IQ样本
- **包数限制**：最多处理前5个数据包
- **图像尺寸**：时频图自动调整大小以适应显示

## 示例输出

```
=== 整合的IQ数据分析和地图生成器 ===
找到 4 个WAV文件:
  1. 0053_20250723_202435.part1.iq.wav
  2. 0053_20250724_100322.part1.iq.wav
  3. 0053_20250724_165611.part1.iq.wav
  4. 0053_20250725_083011.part1.iq.wav

请选择WAV文件 (1-4) 或按回车使用最新文件: 
使用最新文件: 0053_20250725_083011.part1.iq.wav
正在提取GNSS信息...
使用坐标: 经度=112.995010, 纬度=28.229507
正在生成时频图...
转换后坐标 (GCJ-02): 经度=113.000636, 纬度=28.226141
正在获取地图...
正在合成图像...
合成图像已保存到: integrated_analysis_0053_20250725_083011.part1.iq.png
图像已在默认查看器中打开

✅ 分析完成！
生成的图像包含:
  - 高德卫星地图
  - GNSS坐标标记
  - IQ数据时频图
  - 坐标和文件信息
```

## 注意事项

1. **API密钥**：确保高德地图API密钥有效
2. **网络连接**：需要网络连接获取地图图像
3. **文件格式**：仅支持特定格式的IQS WAV文件
4. **中文字体**：可能出现中文字体警告，但不影响功能
5. **内存使用**：大文件可能需要较多内存

## 扩展功能

可以进一步扩展的功能：
- 支持多个数据包的时频图对比
- 添加频谱分析功能
- 支持不同的地图样式
- 添加信号质量评估
- 支持批量处理多个文件
