#define _CRT_SECURE_NO_WARNINGS
#include<stdio.h>
#include<string.h>
#include<iostream>
#include<fstream>
#include<vector>
#include"msgpack.h"
#include"htra_api.h"
#include<iostream>
#include <filesystem>
using namespace std;

union FloatConversion {//用于将float型数据大小端转换
	float f;
	uint32_t i;
};
union DoubleConversion {//用于将double型数据大小端转换
	double d;
	uint64_t i;
};

//此函数用于获取记录文件中的配置信息与迹线信息，数据包数以及迹线的条数。
int SWP_RecordingConfigurationInfo(const char* FilePath, SWP_Profile_TypeDef* SWP_Profile, SWP_TraceInfo_TypeDef* SWP_TraceInfo, uint32_t* TraceNumbers)
{
	int Status = 0;                              //函数返回值变量。
	fstream file;								 //创建文件流对象，用于读取文件。
	string FilePath1 = FilePath;				 //FilePath应为传入的地址。
	file.open(FilePath1, ios::in | ios::binary); //打开文件，采用二进制模式读取。
	
	if (!file.is_open()) //检查文件是否成功打开。
	{
		Status = -1000;
		cout << "File opening failed!" << endl;
		return Status;
	}

	uint64_t PacketCount;     //读取文件中的PacketCount（数据包数量）。
	file.seekg(64, ios::beg); //文件指针偏移64，读取PacketCount的值。
	file.read(reinterpret_cast<char*>(&PacketCount), sizeof(PacketCount));

	
	uint64_t PacketCount_1 = (((PacketCount & 0xff) << 56) | ((PacketCount & 0xff00) << 40) |
		((PacketCount & 0xff0000) << 24) | ((PacketCount & 0xff000000) << 8) |
		((PacketCount & 0xff00000000) >> 8) | ((PacketCount & 0xff0000000000) >> 24) |
		((PacketCount & 0xff000000000000) >> 40) | ((PacketCount & 0xff00000000000000) >> 56)); //调整PacketCount的字节顺序（大端到小端转换）。

	uint16_t Length;                                                     //读取文件中的Length。
	file.seekg(72 + 10 * 1024 * 1024, ios::beg);                         //文件指针到偏移72 + 10MB，读取Length。
	file.read(reinterpret_cast<char*>(&Length), sizeof(Length));
	uint16_t Length_1 = ((Length & 0xff) << 8 | (Length & 0xff00) >> 8); //调整Length字节顺序（大端到小端）。
	char* ConfigurationInfo = new char[Length_1];			             // 根据Length_1动态分配内存空间。
	vector<char> ConfigurationInfo1(Length_1);				             // 使用std::vector来存储读取的内容。

	file.read(ConfigurationInfo1.data(), ConfigurationInfo1.size());				//读取文件中的配置数据到ConfigurationInfo1。
	memcpy(ConfigurationInfo, ConfigurationInfo1.data(), ConfigurationInfo1.size());//复制配置数据到char数组中。

	//检查读取配置文件的内容是否成功。
	if (ConfigurationInfo == NULL)
	{
		Status = -1001;  
		return Status;   
	}

	msgpack_unpacked msg;		 //定义一个解包对象。
	msgpack_unpacked_init(&msg); //初始化msgpack解包对象。

	size_t length = Length_1; //配置数据的长度，对应读取的Length_1。
	size_t off = 0;			  //初始化偏移量为0，表示从数据的起始位置开始解包。
	
	SWP_Profile_TypeDef Profile; //创建一个结构体Profile，存储解包后的配置信息。

	// 解包StartFreq_Hz（起始频率，单位Hz）。
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off); //解包下一个msgpack对象。
	Profile.StartFreq_Hz = (double)msg.data.via.f64;		    //将解包得到的64位浮点数值转换为double，并存入Profile.StartFreq_Hz。

	// 解包StopFreq_Hz（终止频率，单位Hz）。
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off); //解包下一个msgpack对象。
	Profile.StopFreq_Hz = (double)msg.data.via.f64;             //将解包得到的64位浮点数值转换为double，并存入Profile.StopFreq_Hz。

	// 解包CenterFreq_Hz（中心频率，单位Hz）。
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off); //解包下一个msgpack对象。
	Profile.CenterFreq_Hz = (double)msg.data.via.f64;		    //将解包得到的64位浮点数值转换为double，并存入Profile.CenterFreq_Hz。
	//后续同上述操作。
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.Span_Hz = (double)msg.data.via.f64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.RefLevel_dBm = (double)msg.data.via.f64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.RBW_Hz = (double)msg.data.via.f64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.VBW_Hz = (double)msg.data.via.f64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.SweepTime = (double)msg.data.via.f64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.TraceBinSize_Hz = (double)msg.data.via.f64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.FreqAssignment = (SWP_FreqAssignment_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.Window = (Window_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.RBWMode = (RBWMode_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.VBWMode = (VBWMode_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.SweepTimeMode = (SweepTimeMode_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.Detector = (Detector_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.TraceFormat = (TraceFormat_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.TraceDetectMode = (TraceDetectMode_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.TraceDetector = (TraceDetector_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.TracePoints = (uint32_t)msg.data.via.u64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.TracePointsStrategy = (TracePointsStrategy_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.TraceAlign = (TraceAlign_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.FFTExecutionStrategy = (FFTExecutionStrategy_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.RxPort = (RxPort_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.SpurRejection = (SpurRejection_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.ReferenceClockSource = (ReferenceClockSource_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.ReferenceClockFrequency = (double)msg.data.via.f64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.EnableReferenceClockOut = (uint8_t)msg.data.via.u64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.SystemClockSource = (SystemClockSource_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.ExternalSystemClockFrequency = (double)msg.data.via.u64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.TriggerSource = (SWP_TriggerSource_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.TriggerEdge = (TriggerEdge_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.TriggerOutMode = (TriggerOutMode_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.TriggerOutPulsePolarity = (TriggerOutPulsePolarity_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.PowerBalance = (uint32_t)msg.data.via.u64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.GainStrategy = (GainStrategy_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.Preamplifier = (PreamplifierState_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.AnalogIFBWGrade = (uint8_t)msg.data.via.u64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.IFGainGrade = (uint8_t)msg.data.via.u64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.EnableDebugMode = (uint8_t)msg.data.via.u64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.CalibrationSettings = (uint8_t)msg.data.via.u64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.Atten = (int8_t)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.TraceType = (SWP_TraceType_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	Profile.LOOptimization = (LOOptimization_TypeDef)msg.data.via.i64;

	off = off * 2; //将偏移量 off 乘以 2，用于跳过某些数据块或调整解包位置。
	*SWP_Profile = Profile; //将 Profile 结构体的内容复制到 SWP_Profile 指针所指向的内存位置。
	SWP_TraceInfo_TypeDef TraceInfo; //定义一个 SWP_TraceInfo_TypeDef 类型的变量 TraceInfo，用于存储迹线信息。

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off); //从ConfigurationInfo 数据中开始解包，解包后的数据存储在 msg 中。length 是数据的总长度，off 是当前解包偏移量，解包后 off 会更新为新的偏移位置。
	TraceInfo.FullsweepTracePoints = (int)msg.data.via.i64;    //msg.data.via.i64 是解包后的 64 位整型数据，表示迹线点数。

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	TraceInfo.PartialsweepTracePoints = (int)msg.data.via.i64; //msg.data.via.i64 是解包后的 64 位整型数据，表示一帧迹线点数。
	//后续注释同上
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	TraceInfo.TotalHops = (int)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	TraceInfo.UserStartIndex = (uint32_t)msg.data.via.u64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);

	TraceInfo.UserStopIndex = (uint32_t)msg.data.via.u64;
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);

	TraceInfo.TraceBinBW_Hz = (double)msg.data.via.f64;
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);

	TraceInfo.StartFreq_Hz = (double)msg.data.via.f64;
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);

	TraceInfo.AnalysisBW_Hz = (double)msg.data.via.f64;
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);

	TraceInfo.TraceDetectRatio = (int)msg.data.via.i64;
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);

	TraceInfo.DecimateFactor = (int)msg.data.via.i64;
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);

	TraceInfo.FrameTimeMultiple = (float)msg.data.via.f64;
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);

	TraceInfo.FrameTime = (double)msg.data.via.f64;
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);

	TraceInfo.EstimateMinSweepTime = (double)msg.data.via.f64;
	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);

	TraceInfo.DataFormat = (DataFormat_TypeDef)msg.data.via.i64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	TraceInfo.SamplePoints = (uint64_t)msg.data.via.u64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	TraceInfo.GainParameter = (uint32_t)msg.data.via.u64;

	msgpack_unpack_next(&msg, ConfigurationInfo, length, &off);
	TraceInfo.DSPPlatform = (DSPPlatform_Typedef)msg.data.via.u64;
	
	msgpack_unpacked_destroy(&msg); //销毁解包对象。
	*SWP_TraceInfo = TraceInfo;     //将 TraceInfo 结构体的内容复制到 SWP_TraceInfo 指针所指向的位置。

	uint32_t TraceNumber1 = PacketCount_1 / (TraceInfo.FullsweepTracePoints / TraceInfo.PartialsweepTracePoints); //计算基于点数比率的迹线数量（整数形式）。
	double TraceNumber2 = (double)PacketCount_1 / (double)(TraceInfo.FullsweepTracePoints / TraceInfo.PartialsweepTracePoints); //使用浮点数计算追踪数量，避免整数除法带来的误差。
	
	//如果浮点结果大于整数结果，向上取整。
	if (TraceNumber2 > TraceNumber1)
	{
		TraceNumber1++;
	}
	
	*TraceNumbers = TraceNumber1; //将最终的迹线数量传递给外部指针。
	file.close();                 //关闭文件，释放相关资源。
	return 0;
}

//此函数用于获取选择的记录文件的迹线的频率数组与功率数组以及MeasAuxInfo数据包的信息。
int SWP_RecordingPowerSpectralDensityInfo(const char* FilePath,double Freq_Hz[], float PowerSpec_dBm[], MeasAuxInfo_TypeDef MeasAuxInfo[])
{
	
	int Status = 0; //定义函数返回值变量。
	fstream file; //创建 fstream 对象，用于打开并读取文件。
	string FilePath1 = FilePath; //将文件路径存储在一个字符串变量中。
	file.open(FilePath1, ios::in | ios::binary);//以二进制模式打开文件，进行读取操作。

	//检查文件是否成功打开。
	if (!file.is_open())
	{
		cout << "The log file failed to open！" << endl;
		Status = -1000;
		return Status; //返回错误状态。
	}

	uint32_t APIVersion;																																     //读取API版本信息。
	file.seekg(8, ios::beg);																																 //将文件指针定位到文件的第8个字节位置。
	file.read(reinterpret_cast<char*>(&APIVersion), sizeof(APIVersion));																					 //从文件中读取 API 版本，存储到 APIVersion 变量中。
	uint32_t APIVersion_1 = ((APIVersion & 0xFF) << 24) | ((APIVersion & 0xFF00) << 8) | ((APIVersion & 0xFF0000) >> 8) | ((APIVersion & 0xFF000000) >> 24); //将字节顺序从大端字节序转换为小端字节序。
	uint16_t x = (uint16_t)(((APIVersion_1) & 0xffff0000) >> 16);
	uint8_t y = (uint8_t)(((APIVersion_1) & 0x0000ff00) >> 8);
	uint8_t z = (uint8_t)((APIVersion_1) & 0x000000ff);

	//检查是否为55版本。
	if (y != 55)
	{
		cout << "The file is not version 55, please select the version 55 file" << endl;
		Status = -1002;
		return Status;
	}

	uint64_t PacketCount;																											      //读取记录文件中的总数据包数。
	file.seekg(64, ios::beg);																											  //将文件指针定位到文件的第64个字节位置。
	file.read(reinterpret_cast<char*>(&PacketCount), sizeof(PacketCount));																  //从文件中读取数据包数量，存储到 PacketCount 变量中。
	uint64_t PacketCount_1 = (((PacketCount & 0xff) << 56) 
		| ((PacketCount & 0xff00) << 40) | ((PacketCount & 0xff0000) << 24) 
		| ((PacketCount & 0xff000000) << 8) | ((PacketCount & 0xff00000000) >> 8) 
		| ((PacketCount & 0xff0000000000) >> 24) | ((PacketCount & 0xff000000000000) >> 40) | ((PacketCount & 0xff00000000000000) >> 56));//将字节顺序从大端字节序转换为小端字节序。

	uint16_t Length; //读取频率数组、功率数组等数据包长度。
	file.seekg(72 + 10 * 1024 * 1024, ios::beg);
	file.read(reinterpret_cast<char*>(&Length), sizeof(Length));
	uint16_t Length_1 = ((Length & 0xff) << 8 | (Length & 0xff00) >> 8);

	//存储文件内容和数据包解析。
	char ch;                     //临时变量，用于逐个读取文件中的字符。
	vector<char>MeasAuxInfo_ALL; //存储所有读取到的数据包内容。

	//定位到数据包开始的位置。
	file.seekg(74 + 10 * 1024 * 1024 + Length_1, ios::beg); //74: 文件头部偏移，10MB: 跳过前 10MB 数据，Length_1: 动态计算的偏移量。

	while (file.get(ch)) //持续读取文件中的每个字符，直到文件结束。
	{
		MeasAuxInfo_ALL.push_back(ch);
	}

	vector<double>Freq_all; //存储double类型的频率数据。 
	vector<float>Power_all; //存储float类型的功率数据。
	uint32_t index = 0;     //当前数据包的索引，初始化为 0。
    double Freq;            //用于存储当前数据包的频率。
	float Power_1 = 0;      //用于存储当前数据包的功率，初始化为 0。

	for (uint64_t i = 0; i < PacketCount_1; i++) //遍历所有数据包。
	{
		int CountLength;																																		   //一个数据包字节长度。
		memcpy(&CountLength, MeasAuxInfo_ALL.data() + index, sizeof(int));																						   //从数据中读取字节长度。
		int CountLength_1 = (((CountLength & 0xff) << 24) | ((CountLength & 0xff00) << 8) | ((CountLength & 0xff0000) >> 8) | ((CountLength & 0xff000000) >> 24)); //转换字节序（大端 -> 小端）。
		
		int points = ((CountLength_1 - 56) / 12);					  //计算有效数据点数，去掉固定头部56字节（每点12字节）。
		double points1 = ((double)(CountLength_1 - 56) / (double)12); //计算精确点数（浮动值），避免整数除法误差。

		//如果浮动点数大于整数点数，增加一个点以确保数据完整。
		if (points1 > points) //判断一包数据的频率数组大小。
		{
			points += 1;
		}
		
		index += sizeof(int);           //更新索引，跳过 CountLength 字段，准备读取频率数据。
		for (int i = 0; i < points; i++) //读取频率数据（每个频率占8字节）。
		{
			memcpy(&Freq, MeasAuxInfo_ALL.data() + index, sizeof(double));
			Freq_all.push_back(Freq); //将频率值存储到 Freq_all 容器。
			index += sizeof(double); //更新索引，指向下一个数据。
		}
		
		for (int i = 0; i < points; i++) //读取功率数据（每个功率占4字节）。
		{
			memcpy(&Power_1, MeasAuxInfo_ALL.data() + index, sizeof(float));
			Power_all.push_back(Power_1); //将功率值存储到 Power_all 容器。
			index += sizeof(float);       //更新索引，指向下一个数据。
		}
		
		int Hopindex;													//读取Hopindex值并进行字节序转换。
		memcpy(&Hopindex, MeasAuxInfo_ALL.data() + index, sizeof(int)); //从数据中读取Hopindex（4字节）。
		
		index += sizeof(int);                                                                                                                     //更新索引，指向下一个数据位置。
		int Hopindex_1 = ((Hopindex & 0xFF) << 24) | ((Hopindex & 0xFF00) << 8) | ((Hopindex & 0xFF0000) >> 8) | ((Hopindex & 0xFF000000) >> 24); //将Hopindex进行大端到小端字节序转换。

		int Frameindex;																																		//读取Frameindex值并进行字节序转换。
		memcpy(&Frameindex, MeasAuxInfo_ALL.data() + index, sizeof(int));																					//从数据中读取Frameindex（4字节）。
		index += sizeof(int);																													            // 更新索引，指向下一个数据位置。
		int Frameindex_1 = ((Frameindex & 0xFF) << 24) | ((Frameindex & 0xFF00) << 8) | ((Frameindex & 0xFF0000) >> 8) | ((Frameindex & 0xFF000000) >> 24); //将Frameindex进行大端到小端字节序转换。
	
		uint32_t MaxIndex;													 //读取MaxIndex值并进行字节序转换。
		memcpy(&MaxIndex, MeasAuxInfo_ALL.data() + index, sizeof(uint32_t)); // 从数据中读取MaxIndex（4字节，uint32_t类型）。
		
		index += sizeof(uint32_t);                                                                                                                     //更新索引，指向下一个数据位置。
		uint32_t MaxIndex_1 = ((MaxIndex & 0xFF) << 24) | ((MaxIndex & 0xFF00) << 8) | ((MaxIndex & 0xFF0000) >> 8) | ((MaxIndex & 0xFF000000) >> 24); //将MaxIndex进行大端到小端字节序转换。
		MeasAuxInfo[i].MaxIndex = MaxIndex_1;                                                                                                          //将转换后的MaxIndex存入结构体MeasAuxInfo。

		union FloatConversion u;																							  //定义一个联合体，用于转换float和int的字节表示。
		memcpy(&u.f, MeasAuxInfo_ALL.data() + index, sizeof(float));														  //从数据中读取MaxPower（4字节）。
		index += sizeof(float);																								  //更新索引，指向下一个数据位置。
		float MaxPower = ((u.i & 0xFF) << 24) | ((u.i & 0xFF00) << 8) | ((u.i & 0xFF0000) >> 8) | ((u.i & 0xFF000000) >> 24); // 将MaxPower的字节序从大端转换为小端。
		u.i = MaxPower;																										  // 将调整后的字节序赋值回联合体。
		MeasAuxInfo[i].MaxPower_dBm = u.f;																					  // 将转换后的MaxPower（以dBm表示）存入结构体MeasAuxInfo。

		//后续注释同上。
		int16_t Temperature;
		memcpy(&Temperature, MeasAuxInfo_ALL.data() + index, sizeof(int16_t));
		index += sizeof(int16_t);
		int16_t Temperature_1 = ((Temperature & 0xff) << 8 | (Temperature & 0xff00) >> 8);
		MeasAuxInfo[i].Temperature = Temperature_1 * 0.01;

		uint16_t RFState;
		memcpy(&RFState, MeasAuxInfo_ALL.data() + index, sizeof(uint16_t));
		index += sizeof(uint16_t);
		uint16_t RFState_1 = ((RFState & 0xff) << 8 | (RFState & 0xff00) >> 8);
		MeasAuxInfo[i].RFState = RFState_1;

		uint16_t BBState;
		memcpy(&BBState, MeasAuxInfo_ALL.data() + index, sizeof(uint16_t)); 
		index += sizeof(uint16_t);
		uint16_t BBState_1 = ((BBState & 0xff) << 8 | (BBState & 0xff00) >> 8);
		MeasAuxInfo[i].BBState = BBState_1;

		uint16_t GainPattern;
		memcpy(&GainPattern, MeasAuxInfo_ALL.data() + index, sizeof(uint16_t));
		index += sizeof(uint16_t);
		uint16_t GainPattern_1 = ((GainPattern & 0xff) << 8 | (GainPattern & 0xff00) >> 8);
		MeasAuxInfo[i].GainPattern = GainPattern_1;

		uint32_t ConvertPattern;
		memcpy(&ConvertPattern, MeasAuxInfo_ALL.data() + index, sizeof(uint32_t));
		index += sizeof(uint32_t);
		uint32_t ConvertPattern_1 = ((ConvertPattern & 0xFF) << 24) | ((ConvertPattern & 0xFF00) << 8) | ((ConvertPattern & 0xFF0000) >> 8) | ((ConvertPattern & 0xFF000000) >> 24);
		MeasAuxInfo[i].ConvertPattern = ConvertPattern_1;

		union DoubleConversion q;
		memcpy(&q.d, MeasAuxInfo_ALL.data() + index, sizeof(double));
		index += sizeof(double);
		uint64_t SysTimeStamp = ((q.i & 0xFF) << 56) | ((q.i & 0xFF00) << 40) | ((q.i & 0xFF0000) << 24) | ((q.i & 0xFF000000) << 8) | ((q.i >> 8) & 0xFF000000) | ((q.i >> 24) & 0xFF0000) | ((q.i >> 40) & 0xFF00) | ((q.i >> 56) & 0xFF);
		q.i = SysTimeStamp;
		MeasAuxInfo[i].SysTimeStamp = q.d;

		union DoubleConversion A;
		memcpy(&A.d, MeasAuxInfo_ALL.data() + index, sizeof(double));
		index += sizeof(double);
		uint64_t AbsoluteTimeStamp = ((A.i & 0xFF) << 56) | ((A.i & 0xFF00) << 40) | ((A.i & 0xFF0000) << 24) | ((A.i & 0xFF000000) << 8) | ((A.i >> 8) & 0xFF000000) | ((A.i >> 24) & 0xFF0000) | ((A.i >> 40) & 0xFF00) | ((A.i >> 56) & 0xFF);
		A.i = AbsoluteTimeStamp;
		MeasAuxInfo[i].AbsoluteTimeStamp = A.d;

		union FloatConversion L;
		memcpy(&L.f, MeasAuxInfo_ALL.data() + index, sizeof(float));
		index += sizeof(float);
		float Latitude = ((L.i & 0xFF) << 24) | ((L.i & 0xFF00) << 8) | ((L.i & 0xFF0000) >> 8) | ((L.i & 0xFF000000) >> 24);
		L.i = Latitude;
		MeasAuxInfo[i].Latitude = L.f;

		union FloatConversion Lo;
		memcpy(&Lo.f, MeasAuxInfo_ALL.data() + index, sizeof(float));
		index += sizeof(float);
		float Longitude = ((Lo.i & 0xFF) << 24) | ((Lo.i & 0xFF00) << 8) | ((Lo.i & 0xFF0000) >> 8) | ((Lo.i & 0xFF000000) >> 24);
		Lo.i = Longitude;
		MeasAuxInfo[i].Longitude = Lo.f;
	}
	//遍历Freq_all数组，逐个元素将其赋值给Freq_Hz数组。
	for (int i = 0; i < Freq_all.size(); i++)
	{
		Freq_Hz[i] = Freq_all[i];
		PowerSpec_dBm[i] = Power_all[i];
	}
	file.close(); //关闭文件，释放文件资源。
	return 0;
}
int SWPMode_PlayBack()
{
	
	int Status = 0;														 //定义函数返回值变量。
	const char* FilePath = "./data/0053_20250805_172344.part1.spectrum"; //请按需修改记录文件名称如：0022_20241225_094515.part1.spectrum。
	SWP_Profile_TypeDef SWP_Profile;									 //存储SWP_Profile_TypeDef结构体。
	SWP_TraceInfo_TypeDef TraceInfo;									 //存储SWP_TraceInfo_TypeDef结构体。
	uint32_t TraceNumber = 0;											 //定义迹线条数。

	string FilePath_last = "./data/";		  //文件保存路径为 ./data/。
	if (!(filesystem::exists(FilePath_last))) //检查目录是否已存在，如果不存在则创建。
	{
		filesystem::create_directories(FilePath_last); //尝试创建目录及其父目录。
		cout << "dataFolder address :Htra_RecordingandPlayBack\data" << endl;
		if (filesystem::create_directories(FilePath_last)) {
			cout << "Failed to create a directory: \"" << FilePath_last << "\" " << endl;
			return 0;
		}
	}

	Status = SWP_RecordingConfigurationInfo(FilePath, &SWP_Profile, &TraceInfo, &TraceNumber); //从指定文件读取记录配置信息、迹线信息和迹线条数。
	if (Status != 0)																		   //如果读取配置失败，输出错误信息并返回。
	{
		cout << "There was a problem reading the file!" << endl;
		return 0;
	}

	vector<float>Power(TraceInfo.FullsweepTracePoints * TraceNumber);          //存放记录文件的幅度值。
	vector<double>Freq(TraceInfo.FullsweepTracePoints * TraceNumber);          //存放记录文件的频率值。
	vector<MeasAuxInfo_TypeDef>MeasAuxInfo(TraceInfo.TotalHops * TraceNumber); //存储MeasAuxInfo结构体信息。

	Status = SWP_RecordingPowerSpectralDensityInfo(FilePath, Freq.data(), Power.data(), MeasAuxInfo.data()); //调用函数从文件读取功率谱密度数据。
	
	char FileName[255];                           //定义文件名字符数组。
	sprintf(FileName, "./data/SWPMode_Data.txt"); //格式化文件名为 "SWPMode_Data.txt"。
	fstream File1;                                //创建一个fstream对象来操作文件。
	File1.open(FileName, ios::out);               //以写入模式打开文件。
	
	for (int j = 0; j < TraceInfo.FullsweepTracePoints * TraceNumber; j++) //将频率和功率数据写入文件，每行输出一个频率和功率值，使用制表符分隔。
	{
		File1 << Freq[j] << "\t" << Power[j] << "\n";
	}
	
	File1.close(); //关闭文件流，保存并释放资源。

	return 0;
}