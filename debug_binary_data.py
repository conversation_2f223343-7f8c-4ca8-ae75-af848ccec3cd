#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script to examine binary data structure
"""

import struct

def debug_binary_data():
    """Debug the binary data structure"""
    file_path = "./data/0053_20250805_172344.part1.spectrum"
    
    try:
        with open(file_path, 'rb') as file:
            # Skip to measurement data
            file.seek(72 + 10 * 1024 * 1024)
            length_bytes = file.read(2)
            length_raw = struct.unpack('<H', length_bytes)[0]
            length = ((length_raw & 0xff) << 8 | (length_raw & 0xff00) >> 8)
            
            # Go to measurement data
            file.seek(74 + 10 * 1024 * 1024 + length)
            
            # Read first 200 bytes and examine
            data = file.read(200)
            
            print("First 200 bytes of measurement data:")
            for i in range(0, min(200, len(data)), 16):
                hex_str = ' '.join(f'{b:02X}' for b in data[i:i+16])
                ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+16])
                print(f"{i:04X}: {hex_str:<48} {ascii_str}")
            
            print("\nTrying different interpretations of first packet:")
            
            # Try to read first 4 bytes as packet length with different byte orders
            if len(data) >= 4:
                length_le = struct.unpack('<I', data[0:4])[0]
                length_be = struct.unpack('>I', data[0:4])[0]
                length_manual = ((data[0] & 0xff) << 24) | ((data[1] & 0xff) << 16) | ((data[2] & 0xff) << 8) | (data[3] & 0xff)
                
                print(f"First 4 bytes as length:")
                print(f"  Little-endian: {length_le}")
                print(f"  Big-endian: {length_be}")
                print(f"  Manual swap: {length_manual}")
                print(f"  Raw bytes: {data[0]:02X} {data[1]:02X} {data[2]:02X} {data[3]:02X}")
            
            # Look for patterns that might indicate GPS coordinates
            print(f"\nLooking for GPS-like patterns (32-bit floats):")
            for offset in range(0, min(100, len(data)-4), 4):
                try:
                    val_le = struct.unpack('<f', data[offset:offset+4])[0]
                    val_be = struct.unpack('>f', data[offset:offset+4])[0]
                    
                    # Check if values look like GPS coordinates
                    if -90 <= val_le <= 90 or -180 <= val_le <= 180:
                        print(f"  Offset {offset:3d}: LE={val_le:12.6f}, BE={val_be:12.6f} (LE looks like GPS)")
                    elif -90 <= val_be <= 90 or -180 <= val_be <= 180:
                        print(f"  Offset {offset:3d}: LE={val_le:12.6f}, BE={val_be:12.6f} (BE looks like GPS)")
                except:
                    pass
                    
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    debug_binary_data()
