# iq_data.h5 文件结构报告

## 概述

`iq_data.h5` 文件包含6个主要组（Group）:
- BPSKI1
- BPSKI2
- BPSKQ1
- BPSKQ2
- QPSK1
- QPSK2

## 详细结构

### BPSKI1
- 包含10个子组: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
- 每个子组包含2个数据集: I_data 和 Q_data
- 每个数据集形状: (16242,)
- 数据类型: int64
- 总数据点: 324,840 (I_data: 162,420 + Q_data: 162,420)
- 数据样例 (子组 '1'):
  - I_data 前5个元素: [-2 -1 -1 -2 -1]
  - Q_data 前5个元素: [-3 -5 -4 -1  2]

### BPSKI2
- 包含10个子组: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
- 每个子组包含2个数据集: I_data 和 Q_data
- 每个数据集形状: (16242,)
- 数据类型: int64
- 总数据点: 324,840 (I_data: 162,420 + Q_data: 162,420)
- 数据样例 (子组 '1'):
  - I_data 前5个元素: [3 4 3 5 2]
  - Q_data 前5个元素: [ 0 -2 -5 -3 -3]

### BPSKQ1
- 包含10个子组: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
- 每个子组包含2个数据集: I_data 和 Q_data
- 每个数据集形状: (16242,)
- 数据类型: int64
- 总数据点: 324,840 (I_data: 162,420 + Q_data: 162,420)
- 数据样例 (子组 '1'):
  - I_data 前5个元素: [6 7 4 6 5]
  - Q_data 前5个元素: [ 0 -3 -2 -4  0]

### BPSKQ2
- 包含10个子组: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
- 每个子组包含2个数据集: I_data 和 Q_data
- 每个数据集形状: (16242,)
- 数据类型: int64
- 总数据点: 324,840 (I_data: 162,420 + Q_data: 162,420)
- 数据样例 (子组 '1'):
  - I_data 前5个元素: [-2 -1 -2  2 -1]
  - Q_data 前5个元素: [3 5 5 3 8]

### QPSK1
- 包含10个子组: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
- 每个子组包含2个数据集: I_data 和 Q_data
- 每个数据集形状: (16242,)
- 数据类型: int64
- 总数据点: 324,840 (I_data: 162,420 + Q_data: 162,420)
- 数据样例 (子组 '1'):
  - I_data 前5个元素: [4 4 8 3 7]
  - Q_data 前5个元素: [5 0 1 0 3]

### QPSK2
- 包含8个子组: ['1', '2', '3', '4', '5', '6', '7', '10']
- 每个子组包含2个数据集: I_data 和 Q_data
- 每个数据集形状: (16242,)
- 数据类型: int64
- 总数据点: 227,388 (I_data: 113,694 + Q_data: 113,694)
- 数据样例 (子组 '1'):
  - I_data 前5个元素: [-7 -8 -9 -5 -5]
  - Q_data 前5个元素: [-3 -5 -5 -5 -2]

## 总结

- 文件包含6个主要组
- 总共包含58个子组
- 每个子组包含I_data和Q_data两个数据集
- 所有数据集的数据类型均为int64
- 每个数据集的形状均为(16242,)
- 文件总数据点: 1,851,588 