#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级时间戳分析脚本
尝试更多可能的时间戳格式，特别针对2025年7月23日20:24:35
"""

import struct
from datetime import datetime, timezone, timedelta
import os

def analyze_special_timestamp_formats(timestamp_value, target_datetime_str="2025-07-23 20:24:35"):
    """
    分析特殊的时间戳格式
    """
    print(f"原始时间戳: {timestamp_value}")
    print(f"目标时间: {target_datetime_str}")
    print(f"时间戳的十六进制: 0x{int(timestamp_value):X}")
    print(f"时间戳的二进制长度: {int(timestamp_value).bit_length()} 位")
    print("-" * 80)
    
    # 目标时间
    target_dt = datetime.strptime(target_datetime_str, "%Y-%m-%d %H:%M:%S")
    target_timestamp = target_dt.timestamp()
    
    print(f"目标Unix时间戳: {target_timestamp}")
    print(f"目标时间的十六进制: 0x{int(target_timestamp):X}")
    
    # 尝试各种可能的时间戳格式
    print("\n尝试各种时间戳格式:")
    print("-" * 80)
    
    # 1. 可能是从某个特定epoch开始的时间戳
    possible_epochs = [
        ("Unix Epoch (1970-01-01)", datetime(1970, 1, 1)),
        ("Windows Epoch (1601-01-01)", datetime(1601, 1, 1)),
        ("GPS Epoch (1980-01-06)", datetime(1980, 1, 6)),
        ("NTP Epoch (1900-01-01)", datetime(1900, 1, 1)),
        ("设备制造年 (2020-01-01)", datetime(2020, 1, 1)),
        ("设备制造年 (2021-01-01)", datetime(2021, 1, 1)),
        ("设备制造年 (2022-01-01)", datetime(2022, 1, 1)),
        ("设备制造年 (2023-01-01)", datetime(2023, 1, 1)),
        ("设备制造年 (2024-01-01)", datetime(2024, 1, 1)),
    ]
    
    best_matches = []
    
    for epoch_name, epoch_dt in possible_epochs:
        try:
            epoch_timestamp = epoch_dt.timestamp()
        except OSError:
            # 对于1601年等早期日期，使用手动计算
            if epoch_dt.year == 1601:
                epoch_timestamp = -11644473600  # 1601到1970的秒数差
            elif epoch_dt.year == 1900:
                epoch_timestamp = -2208988800  # 1900到1970的秒数差
            else:
                continue
        
        # 尝试不同的时间单位
        time_units = [
            ("秒", 1),
            ("毫秒", 1000),
            ("微秒", 1000000),
            ("纳秒", 1000000000),
            ("100纳秒", 10000000),
        ]
        
        for unit_name, divisor in time_units:
            try:
                # 计算从epoch开始的时间
                calculated_timestamp = epoch_timestamp + (timestamp_value / divisor)
                
                if calculated_timestamp > 0:
                    calculated_dt = datetime.fromtimestamp(calculated_timestamp, tz=timezone.utc)
                    diff = abs(calculated_timestamp - target_timestamp)
                    
                    result = {
                        'format': f"{epoch_name} + {unit_name}",
                        'datetime': calculated_dt,
                        'timestamp': calculated_timestamp,
                        'diff_seconds': diff,
                        'diff_days': diff / 86400
                    }
                    
                    best_matches.append(result)
                    
                    print(f"{epoch_name:25} + {unit_name:8}: {calculated_dt.strftime('%Y-%m-%d %H:%M:%S.%f UTC')} (差值: {diff/86400:.2f}天)")
                    
            except (ValueError, OSError, OverflowError):
                continue
    
    # 2. 尝试将时间戳分解为不同部分
    print(f"\n时间戳分解分析:")
    print("-" * 80)
    
    timestamp_int = int(timestamp_value)
    
    # 尝试高位和低位分解
    if timestamp_int.bit_length() <= 64:
        # 32位分解
        high_32 = (timestamp_int >> 32) & 0xFFFFFFFF
        low_32 = timestamp_int & 0xFFFFFFFF
        
        print(f"高32位: {high_32} (0x{high_32:08X})")
        print(f"低32位: {low_32} (0x{low_32:08X})")
        
        # 尝试高32位作为秒，低32位作为分数秒
        try:
            combined_timestamp = high_32 + low_32 / (2**32)
            combined_dt = datetime.fromtimestamp(combined_timestamp, tz=timezone.utc)
            diff = abs(combined_timestamp - target_timestamp)
            print(f"组合时间（高32位秒+低32位分数）: {combined_dt.strftime('%Y-%m-%d %H:%M:%S.%f UTC')} (差值: {diff/86400:.2f}天)")
            
            best_matches.append({
                'format': '高32位秒+低32位分数',
                'datetime': combined_dt,
                'timestamp': combined_timestamp,
                'diff_seconds': diff,
                'diff_days': diff / 86400
            })
        except:
            pass
        
        # 尝试低32位作为秒，高32位作为其他信息
        try:
            low_timestamp = low_32
            low_dt = datetime.fromtimestamp(low_timestamp, tz=timezone.utc)
            diff = abs(low_timestamp - target_timestamp)
            print(f"低32位作为Unix时间戳: {low_dt.strftime('%Y-%m-%d %H:%M:%S.%f UTC')} (差值: {diff/86400:.2f}天)")
            
            best_matches.append({
                'format': '低32位Unix时间戳',
                'datetime': low_dt,
                'timestamp': low_timestamp,
                'diff_seconds': diff,
                'diff_days': diff / 86400
            })
        except:
            pass
    
    # 3. 尝试特殊的设备时间格式
    print(f"\n特殊设备格式分析:")
    print("-" * 80)
    
    # 可能是从设备启动开始的高精度计时器
    # 假设设备在文件创建时间附近启动
    device_boot_estimates = [
        target_dt - timedelta(hours=1),
        target_dt - timedelta(hours=2),
        target_dt - timedelta(hours=6),
        target_dt - timedelta(hours=12),
        target_dt - timedelta(days=1),
    ]
    
    for i, boot_time in enumerate(device_boot_estimates):
        boot_timestamp = boot_time.timestamp()
        
        # 尝试不同的计时器单位
        for unit_name, divisor in time_units:
            try:
                calculated_timestamp = boot_timestamp + (timestamp_value / divisor)
                calculated_dt = datetime.fromtimestamp(calculated_timestamp, tz=timezone.utc)
                diff = abs(calculated_timestamp - target_timestamp)
                
                if diff < 86400:  # 只显示差值小于1天的结果
                    print(f"设备启动时间-{i+1} + {unit_name:8}: {calculated_dt.strftime('%Y-%m-%d %H:%M:%S.%f UTC')} (差值: {diff:.2f}秒)")
                    
                    best_matches.append({
                        'format': f'设备启动时间-{i+1} + {unit_name}',
                        'datetime': calculated_dt,
                        'timestamp': calculated_timestamp,
                        'diff_seconds': diff,
                        'diff_days': diff / 86400
                    })
            except:
                continue
    
    # 找到最佳匹配
    if best_matches:
        best_matches.sort(key=lambda x: x['diff_seconds'])
        
        print(f"\n最佳匹配结果 (前5个):")
        print("-" * 80)
        for i, match in enumerate(best_matches[:5]):
            print(f"{i+1}. {match['format']}")
            print(f"   时间: {match['datetime'].strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
            print(f"   差值: {match['diff_seconds']:.2f}秒 ({match['diff_days']:.4f}天)")
            print()
    
    return best_matches

def main():
    """主函数"""
    print("=" * 80)
    print("高级时间戳分析 - 寻找2025年7月23日20:24:35对应的格式")
    print("=" * 80)
    
    # 使用之前获取的时间戳值
    timestamp_value = 990956329492035.0
    
    best_matches = analyze_special_timestamp_formats(timestamp_value)
    
    if best_matches and best_matches[0]['diff_seconds'] < 3600:  # 差值小于1小时
        print("=" * 80)
        print("找到可能的正确格式!")
        print("=" * 80)
        best = best_matches[0]
        print(f"格式: {best['format']}")
        print(f"转换后时间: {best['datetime'].strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
        print(f"与目标时间差值: {best['diff_seconds']:.2f}秒")
    else:
        print("=" * 80)
        print("未找到完全匹配的格式")
        print("=" * 80)
        print("建议:")
        print("1. 检查设备文档中的时间戳格式说明")
        print("2. 联系设备厂商确认时间戳格式")
        print("3. 可能需要额外的校准参数")

if __name__ == "__main__":
    main()
