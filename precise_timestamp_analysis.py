#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确时间戳分析脚本
基于已知的目标时间反推时间戳格式
"""

from datetime import datetime, timezone, timedelta
import struct

def reverse_engineer_timestamp(timestamp_value, target_datetime_str="2025-07-23 20:24:35"):
    """
    反向工程时间戳格式
    """
    print(f"原始时间戳: {timestamp_value}")
    print(f"目标时间: {target_datetime_str}")
    print("-" * 80)
    
    # 目标时间
    target_dt = datetime.strptime(target_datetime_str, "%Y-%m-%d %H:%M:%S")
    target_timestamp = target_dt.timestamp()
    
    print(f"目标Unix时间戳: {target_timestamp}")
    
    # 计算可能的epoch起始时间
    time_units = [
        ("秒", 1),
        ("毫秒", 1000),
        ("微秒", 1000000),
        ("纳秒", 1000000000),
        ("100纳秒", 10000000),
    ]
    
    print(f"\n反推可能的epoch起始时间:")
    print("-" * 80)
    
    for unit_name, divisor in time_units:
        # 计算epoch = target_timestamp - (timestamp_value / divisor)
        try:
            calculated_epoch = target_timestamp - (timestamp_value / divisor)
            epoch_dt = datetime.fromtimestamp(calculated_epoch, tz=timezone.utc)
            
            print(f"{unit_name:10}: Epoch = {epoch_dt.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
            
            # 检查这个epoch是否合理
            if epoch_dt.year >= 1980 and epoch_dt.year <= 2025:
                print(f"           -> 这是一个合理的epoch时间!")
                
                # 验证：使用这个epoch计算回目标时间
                verify_timestamp = calculated_epoch + (timestamp_value / divisor)
                verify_dt = datetime.fromtimestamp(verify_timestamp, tz=timezone.utc)
                print(f"           -> 验证: {verify_dt.strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
                
        except (ValueError, OSError, OverflowError):
            print(f"{unit_name:10}: 计算失败")
    
    # 特别检查一些常见的设备epoch
    print(f"\n检查常见设备epoch:")
    print("-" * 80)
    
    common_epochs = [
        ("设备启动 (文件时间-1小时)", target_dt - timedelta(hours=1)),
        ("设备启动 (文件时间-2小时)", target_dt - timedelta(hours=2)),
        ("设备启动 (文件时间-6小时)", target_dt - timedelta(hours=6)),
        ("设备启动 (文件时间-12小时)", target_dt - timedelta(hours=12)),
        ("设备启动 (文件时间-1天)", target_dt - timedelta(days=1)),
        ("当天00:00", target_dt.replace(hour=0, minute=0, second=0, microsecond=0)),
        ("当月1日00:00", target_dt.replace(day=1, hour=0, minute=0, second=0, microsecond=0)),
        ("当年1月1日00:00", target_dt.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)),
        ("GPS Epoch (1980-01-06)", datetime(1980, 1, 6)),
        ("Unix Epoch (1970-01-01)", datetime(1970, 1, 1)),
    ]
    
    best_matches = []
    
    for epoch_name, epoch_dt in common_epochs:
        try:
            epoch_timestamp = epoch_dt.timestamp()
        except OSError:
            if epoch_dt.year == 1980:
                epoch_timestamp = 315964800  # GPS epoch
            elif epoch_dt.year == 1970:
                epoch_timestamp = 0  # Unix epoch
            else:
                continue
        
        for unit_name, divisor in time_units:
            try:
                # 计算从这个epoch到目标时间需要的时间戳值
                required_timestamp_value = (target_timestamp - epoch_timestamp) * divisor
                
                # 检查是否与实际时间戳值接近
                diff = abs(required_timestamp_value - timestamp_value)
                relative_diff = diff / timestamp_value if timestamp_value != 0 else float('inf')
                
                if relative_diff < 0.1:  # 差异小于10%
                    calculated_time = epoch_timestamp + (timestamp_value / divisor)
                    calculated_dt = datetime.fromtimestamp(calculated_time, tz=timezone.utc)
                    time_diff = abs(calculated_time - target_timestamp)
                    
                    match = {
                        'epoch': epoch_name,
                        'unit': unit_name,
                        'calculated_dt': calculated_dt,
                        'time_diff_seconds': time_diff,
                        'timestamp_diff_percent': relative_diff * 100
                    }
                    best_matches.append(match)
                    
                    print(f"{epoch_name:30} + {unit_name:8}: {calculated_dt.strftime('%Y-%m-%d %H:%M:%S.%f UTC')} "
                          f"(时间差: {time_diff:.2f}秒, 时间戳差: {relative_diff*100:.2f}%)")
                    
            except (ValueError, OSError, OverflowError):
                continue
    
    # 尝试更精确的分析
    print(f"\n精确分析 - 寻找最佳匹配:")
    print("-" * 80)
    
    # 基于文件名时间进行更精确的搜索
    file_time_variations = []
    base_time = target_dt
    
    # 生成文件时间附近的各种可能时间点
    for hours_offset in range(-24, 25):
        for minutes_offset in [0, 15, 30, 45]:
            test_time = base_time + timedelta(hours=hours_offset, minutes=minutes_offset)
            file_time_variations.append(test_time)
    
    for test_epoch in file_time_variations[:50]:  # 限制测试数量
        try:
            test_epoch_timestamp = test_epoch.timestamp()
            
            for unit_name, divisor in time_units:
                calculated_time = test_epoch_timestamp + (timestamp_value / divisor)
                
                if abs(calculated_time - target_timestamp) < 3600:  # 差异小于1小时
                    calculated_dt = datetime.fromtimestamp(calculated_time, tz=timezone.utc)
                    time_diff = abs(calculated_time - target_timestamp)
                    
                    match = {
                        'epoch': f"自定义epoch {test_epoch.strftime('%Y-%m-%d %H:%M:%S')}",
                        'unit': unit_name,
                        'calculated_dt': calculated_dt,
                        'time_diff_seconds': time_diff,
                        'timestamp_diff_percent': 0
                    }
                    best_matches.append(match)
                    
        except (ValueError, OSError, OverflowError):
            continue
    
    # 排序并显示最佳匹配
    if best_matches:
        best_matches.sort(key=lambda x: x['time_diff_seconds'])
        
        print(f"\n最佳匹配结果:")
        print("-" * 80)
        for i, match in enumerate(best_matches[:5]):
            print(f"{i+1}. {match['epoch']} + {match['unit']}")
            print(f"   计算时间: {match['calculated_dt'].strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
            print(f"   时间差: {match['time_diff_seconds']:.2f}秒")
            if match['timestamp_diff_percent'] > 0:
                print(f"   时间戳差异: {match['timestamp_diff_percent']:.2f}%")
            print()
            
        if best_matches[0]['time_diff_seconds'] < 60:
            print("找到了非常接近的匹配!")
            return best_matches[0]
    
    return None

def main():
    """主函数"""
    print("=" * 80)
    print("精确时间戳分析 - 反向工程时间戳格式")
    print("=" * 80)
    
    # 使用之前获取的时间戳值
    timestamp_value = 990956329492035.0
    
    result = reverse_engineer_timestamp(timestamp_value)
    
    if result:
        print("=" * 80)
        print("推荐的时间戳格式:")
        print("=" * 80)
        print(f"Epoch: {result['epoch']}")
        print(f"单位: {result['unit']}")
        print(f"转换公式: epoch_timestamp + (timestamp_value / unit_divisor)")
        print(f"验证结果: {result['calculated_dt'].strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
    else:
        print("=" * 80)
        print("未找到精确匹配的格式")
        print("=" * 80)
        print("建议查阅设备技术文档或联系厂商")

if __name__ == "__main__":
    main()
