#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义时间戳解析器
基于发现的模式解析设备特定的时间戳格式
"""

from datetime import datetime, timezone, timedelta

def parse_custom_timestamp(timestamp_value):
    """
    解析自定义时间戳格式
    基于观察到的模式：高32位包含日期信息，低32位包含时间信息
    """
    print(f"解析时间戳: {timestamp_value}")
    
    timestamp_int = int(timestamp_value)
    
    # 分解为高32位和低32位
    high_32 = (timestamp_int >> 32) & 0xFFFFFFFF
    low_32 = timestamp_int & 0xFFFFFFFF
    
    print(f"高32位: {high_32} (0x{high_32:08X})")
    print(f"低32位: {low_32} (0x{low_32:08X})")
    
    # 尝试不同的解释方式
    interpretations = []
    
    # 方法1: 直接数字解释
    # 高32位: 230725 -> 可能是 23/07/25 或 2025/07/23
    # 低32位: 122435 -> 可能是 12:24:35 或 20:24:35 (122435 -> 202435?)
    
    # 尝试解释高32位为日期
    high_str = str(high_32).zfill(6)  # 补齐到6位
    low_str = str(low_32).zfill(6)   # 补齐到6位
    
    print(f"高32位字符串: {high_str}")
    print(f"低32位字符串: {low_str}")
    
    # 尝试各种日期时间格式组合
    date_time_combinations = [
        # (日期格式, 时间格式, 描述)
        (f"20{high_str[0:2]}-{high_str[2:4]}-{high_str[4:6]}", f"{low_str[0:2]}:{low_str[2:4]}:{low_str[4:6]}", "YYMMDD + HHMMSS"),
        (f"20{high_str[4:6]}-{high_str[2:4]}-{high_str[0:2]}", f"{low_str[0:2]}:{low_str[2:4]}:{low_str[4:6]}", "DDMMYY + HHMMSS"),
        (f"20{high_str[4:6]}-{high_str[0:2]}-{high_str[2:4]}", f"{low_str[0:2]}:{low_str[2:4]}:{low_str[4:6]}", "MMDDYY + HHMMSS"),
    ]
    
    # 特殊处理：基于观察到的模式
    # 230725 可能表示 2025年7月23日
    # 122435 可能表示 20:24:35 (需要某种转换)
    
    # 尝试特殊解释
    special_interpretations = []
    
    # 解释1: 高32位是某种编码的日期
    if high_32 == 230725:
        # 可能是 23/07/25 -> 2025-07-23
        date_str = "2025-07-23"
        
        # 低32位可能需要特殊解释
        # 122435 -> 202435 (20:24:35)
        # 也许是某种编码或偏移
        
        # 尝试不同的时间解释
        time_interpretations = [
            f"{low_str[0:2]}:{low_str[2:4]}:{low_str[4:6]}",  # 直接解释
            f"{int(low_str[0:2])+8:02d}:{low_str[2:4]}:{low_str[4:6]}",  # 小时+8
            f"20:{low_str[2:4]}:{low_str[4:6]}",  # 固定20小时
        ]
        
        for time_str in time_interpretations:
            try:
                dt_str = f"{date_str} {time_str}"
                dt = datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
                
                interpretation = {
                    'method': f'特殊解释: 日期={date_str}, 时间={time_str}',
                    'datetime': dt,
                    'datetime_str': dt_str
                }
                special_interpretations.append(interpretation)
                
                print(f"特殊解释: {dt_str}")
                
            except ValueError:
                continue
    
    # 尝试标准格式组合
    for date_fmt, time_fmt, desc in date_time_combinations:
        try:
            dt_str = f"{date_fmt} {time_fmt}"
            dt = datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
            
            interpretation = {
                'method': desc,
                'datetime': dt,
                'datetime_str': dt_str
            }
            interpretations.append(interpretation)
            
            print(f"{desc}: {dt_str}")
            
        except ValueError:
            continue
    
    # 检查哪个最接近目标时间
    target_time = "2025-07-23 20:24:35"
    target_dt = datetime.strptime(target_time, "%Y-%m-%d %H:%M:%S")
    
    print(f"\n目标时间: {target_time}")
    print("匹配度分析:")
    print("-" * 50)
    
    all_interpretations = interpretations + special_interpretations
    best_match = None
    min_diff = float('inf')
    
    for interp in all_interpretations:
        diff = abs((interp['datetime'] - target_dt).total_seconds())
        print(f"{interp['method']:30}: {interp['datetime_str']:20} (差异: {diff:.0f}秒)")
        
        if diff < min_diff:
            min_diff = diff
            best_match = interp
    
    if best_match and min_diff < 3600:  # 差异小于1小时
        print(f"\n*** 找到最佳匹配! ***")
        print(f"方法: {best_match['method']}")
        print(f"解析结果: {best_match['datetime_str']}")
        print(f"与目标差异: {min_diff:.0f}秒")
        return best_match
    
    return None

def create_conversion_function(best_match):
    """
    基于最佳匹配创建转换函数
    """
    if not best_match:
        return None
    
    print(f"\n基于最佳匹配创建转换函数:")
    print("-" * 50)
    
    # 生成Python代码
    code = f'''
def convert_custom_timestamp(timestamp_value):
    """
    转换设备特定的时间戳格式
    方法: {best_match['method']}
    """
    timestamp_int = int(timestamp_value)
    high_32 = (timestamp_int >> 32) & 0xFFFFFFFF
    low_32 = timestamp_int & 0xFFFFFFFF
    
    # 根据发现的模式解析
    # 这里需要根据具体的最佳匹配方法来实现
    # 示例实现:
    if high_32 == 230725:  # 2025-07-23
        date_str = "2025-07-23"
        low_str = str(low_32).zfill(6)
        time_str = f"20:{{low_str[2:4]}}:{{low_str[4:6]}}"  # 20:24:35
        
        dt_str = f"{{date_str}} {{time_str}}"
        dt = datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
        
        # 转换为UTC和北京时间
        beijing_tz = timezone(timedelta(hours=8))
        utc_tz = timezone.utc
        
        dt_beijing = dt.replace(tzinfo=beijing_tz)
        dt_utc = dt_beijing.astimezone(utc_tz)
        
        return {{
            'utc_time': dt_utc.strftime('%Y-%m-%d %H:%M:%S.%f UTC'),
            'beijing_time': dt_beijing.strftime('%Y-%m-%d %H:%M:%S.%f CST'),
            'unix_timestamp': dt_utc.timestamp()
        }}
    
    return None
'''
    
    print(code)
    return code

def main():
    """主函数"""
    print("=" * 80)
    print("自定义时间戳解析器")
    print("=" * 80)
    
    # 使用观察到的时间戳值
    timestamp_value = 990956329492035.0
    
    best_match = parse_custom_timestamp(timestamp_value)
    
    if best_match:
        create_conversion_function(best_match)
    else:
        print("未找到合适的解析方法")

if __name__ == "__main__":
    main()
