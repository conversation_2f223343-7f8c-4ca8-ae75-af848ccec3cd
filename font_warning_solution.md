# 中文字体警告问题解决方案

## 问题描述

运行 `integrated_iq_map_analyzer.py` 时出现大量中文字体警告：

```
UserWarning: Glyph 38388 (\N{CJK UNIFIED IDEOGRAPH-95F4}) missing from font(s) DejaVu Sans.
UserWarning: Glyph 21151 (\N{CJK UNIFIED IDEOGRAPH-529F}) missing from font(s) DejaVu Sans.
```

这些警告是因为matplotlib无法找到合适的中文字体来显示中文字符（如"频率"、"时间"、"功率"等）。

## 解决方案

### 方案1：自动字体配置（已实现）

在代码中添加了智能字体配置功能：

```python
def configure_matplotlib_font():
    """配置matplotlib字体设置"""
    try:
        import matplotlib.font_manager as fm
        
        # 查找系统中可用的中文字体
        chinese_fonts = []
        for font in fm.fontManager.ttflist:
            if 'SimHei' in font.name or 'Microsoft YaHei' in font.name or 'SimSun' in font.name:
                chinese_fonts.append(font.name)
        
        if chinese_fonts:
            plt.rcParams['font.sans-serif'] = chinese_fonts + ['DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            print(f"已配置中文字体: {chinese_fonts[0]}")
        else:
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            print("未找到中文字体，将使用英文标签")
            
    except Exception as e:
        print(f"字体配置警告: {e}")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
```

### 方案2：使用英文标签（已实现）

将时频图中的中文标签改为英文：

- "频率 (MHz)" → "Frequency (MHz)"
- "时间 (ms)" → "Time (ms)"  
- "功率 (dB)" → "Power (dB)"
- "时频图" → "Spectrogram"

### 方案3：手动安装中文字体（可选）

如果系统没有中文字体，可以手动安装：

#### Windows系统：
1. 下载中文字体文件（如SimHei.ttf）
2. 复制到 `C:\Windows\Fonts\` 目录
3. 重启Python程序

#### Linux系统：
```bash
# Ubuntu/Debian
sudo apt-get install fonts-wqy-microhei fonts-wqy-zenhei

# CentOS/RHEL
sudo yum install wqy-microhei-fonts wqy-zenhei-fonts
```

#### macOS系统：
```bash
# 使用Homebrew安装
brew install font-wqy-microhei font-wqy-zenhei
```

## 当前状态

✅ **已解决**：中文字体警告问题  
✅ **已实现**：自动字体检测和配置  
✅ **已实现**：英文标签备用方案  
✅ **保留功能**：所有原有功能正常工作  

## 测试结果

修改后的运行输出：
```
已配置中文字体: Microsoft YaHei
命令行模式: 文件=0053_20250725_083011.part1.iq.wav, 输出=test_no_warning.png
正在提取GNSS信息...
使用坐标: 经度=112.995010, 纬度=28.229507
正在生成时频图...
转换后坐标 (GCJ-02): 经度=113.000636, 纬度=28.226141
正在获取地图...
正在合成图像...
合成图像已保存到: test_no_warning.png

✅ 分析完成！
```

## 剩余警告说明

现在只剩下一个警告：
```
UserWarning: Input data is complex, switching to return_onesided=False
```

这是正常的提示信息，因为我们确实在处理复数IQ数据。这个警告不影响功能，是scipy.signal.stft函数的正常行为提示。

## 进一步优化（可选）

如果想完全消除这个警告，可以在STFT调用时显式设置参数：

```python
f, t, Zxx = signal.stft(complex_signal, 
                       fs=self.sample_rate, 
                       window='hann', 
                       nperseg=256, 
                       noverlap=128,
                       return_onesided=False)  # 显式设置
```

## 总结

通过以上修改，成功解决了中文字体警告问题：

1. **智能字体检测**：自动查找系统可用的中文字体
2. **优雅降级**：如果没有中文字体，自动使用英文标签
3. **用户友好**：显示字体配置状态信息
4. **功能完整**：保持所有原有功能不变

现在程序运行时不再出现大量字体警告，用户体验大大改善。
