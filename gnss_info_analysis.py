#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GNSS信息分析脚本
从IQS WAV文件中提取的GNSS信息进行详细分析
"""

import pandas as pd
import numpy as np
from datetime import datetime, timezone
import matplotlib.pyplot as plt
from main import extract_gnss_info
import os

def timestamp_to_datetime(timestamp):
    """
    将时间戳转换为可读的日期时间格式
    
    Args:
        timestamp: 时间戳值
    
    Returns:
        格式化的日期时间字符串
    """
    try:
        # 尝试不同的时间戳格式
        # 1. 假设是微秒级时间戳
        if timestamp > 1e15:  # 微秒级时间戳
            dt = datetime.fromtimestamp(timestamp / 1e6, tz=timezone.utc)
        # 2. 假设是毫秒级时间戳
        elif timestamp > 1e12:  # 毫秒级时间戳
            dt = datetime.fromtimestamp(timestamp / 1e3, tz=timezone.utc)
        # 3. 假设是秒级时间戳
        elif timestamp > 1e9:  # 秒级时间戳
            dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        else:
            # 可能是其他格式，尝试直接转换
            dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        
        return dt.strftime('%Y-%m-%d %H:%M:%S.%f UTC')
    except (ValueError, OSError) as e:
        return f"无法解析时间戳: {timestamp} (错误: {e})"

def analyze_gnss_data(wav_file_path):
    """
    分析WAV文件中的GNSS数据
    
    Args:
        wav_file_path: WAV文件路径
    """
    print("=" * 80)
    print("GNSS信息详细分析")
    print("=" * 80)
    
    # 提取GNSS信息
    gnss_data = extract_gnss_info(wav_file_path)
    
    if not gnss_data:
        print("未能提取到GNSS信息")
        return
    
    # 转换为DataFrame便于分析
    df = pd.DataFrame(gnss_data)
    
    print(f"\n文件: {wav_file_path}")
    print(f"数据包总数: {len(gnss_data)}")
    print("\n基本统计信息:")
    print("-" * 50)
    
    # 时间戳分析
    print(f"时间戳范围:")
    print(f"  最小值: {df['AbsoluteTimeStamp'].min()}")
    print(f"  最大值: {df['AbsoluteTimeStamp'].max()}")
    print(f"  平均值: {df['AbsoluteTimeStamp'].mean()}")
    
    # 尝试转换时间戳
    print(f"\n时间戳转换尝试:")
    sample_timestamp = df['AbsoluteTimeStamp'].iloc[0]
    print(f"  原始时间戳: {sample_timestamp}")
    print(f"  转换结果: {timestamp_to_datetime(sample_timestamp)}")
    
    # 经纬度分析
    print(f"\n位置信息:")
    print(f"  纬度范围: {df['Latitude'].min():.6f} ~ {df['Latitude'].max():.6f}")
    print(f"  经度范围: {df['Longitude'].min():.6f} ~ {df['Longitude'].max():.6f}")
    print(f"  平均纬度: {df['Latitude'].mean():.6f}")
    print(f"  平均经度: {df['Longitude'].mean():.6f}")
    
    # 检查位置是否变化
    lat_std = df['Latitude'].std()
    lon_std = df['Longitude'].std()
    print(f"  纬度标准差: {lat_std:.8f}")
    print(f"  经度标准差: {lon_std:.8f}")
    
    if lat_std < 1e-6 and lon_std < 1e-6:
        print("  -> 位置基本不变（静止状态）")
    else:
        print("  -> 位置有变化（移动状态）")
    
    # 系统计时器分析
    print(f"\n系统计时器:")
    print(f"  范围: {df['SysTimerCount'].min()} ~ {df['SysTimerCount'].max()}")
    print(f"  平均值: {df['SysTimerCount'].mean()}")
    
    # 详细数据表
    print(f"\n详细数据:")
    print("-" * 100)
    print(f"{'包号':<6} {'时间戳':<20} {'纬度':<12} {'经度':<12} {'系统计时器':<15} {'时间转换':<30}")
    print("-" * 100)
    
    for _, row in df.iterrows():
        time_str = timestamp_to_datetime(row['AbsoluteTimeStamp'])
        print(f"{row['PacketNumber']:<6} "
              f"{row['AbsoluteTimeStamp']:<20.0f} "
              f"{row['Latitude']:<12.6f} "
              f"{row['Longitude']:<12.6f} "
              f"{row['SysTimerCount']:<15.6f} "
              f"{time_str:<30}")
    
    # 保存详细分析结果
    output_file = "./data/gnss_detailed_analysis.csv"
    df_detailed = df.copy()
    df_detailed['DateTime'] = df_detailed['AbsoluteTimeStamp'].apply(timestamp_to_datetime)
    df_detailed.to_csv(output_file, index=False)
    print(f"\n详细分析结果已保存到: {output_file}")
    
    # 生成可视化图表（如果位置有变化）
    if lat_std > 1e-6 or lon_std > 1e-6:
        create_gnss_plots(df)
    
    return df

def create_gnss_plots(df):
    """
    创建GNSS数据的可视化图表
    
    Args:
        df: 包含GNSS数据的DataFrame
    """
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 轨迹图
    axes[0, 0].plot(df['Longitude'], df['Latitude'], 'b-o', markersize=4)
    axes[0, 0].set_xlabel('经度')
    axes[0, 0].set_ylabel('纬度')
    axes[0, 0].set_title('GPS轨迹')
    axes[0, 0].grid(True)
    
    # 2. 纬度随时间变化
    axes[0, 1].plot(df['PacketNumber'], df['Latitude'], 'r-o', markersize=4)
    axes[0, 1].set_xlabel('数据包编号')
    axes[0, 1].set_ylabel('纬度')
    axes[0, 1].set_title('纬度随时间变化')
    axes[0, 1].grid(True)
    
    # 3. 经度随时间变化
    axes[1, 0].plot(df['PacketNumber'], df['Longitude'], 'g-o', markersize=4)
    axes[1, 0].set_xlabel('数据包编号')
    axes[1, 0].set_ylabel('经度')
    axes[1, 0].set_title('经度随时间变化')
    axes[1, 0].grid(True)
    
    # 4. 时间戳变化
    axes[1, 1].plot(df['PacketNumber'], df['AbsoluteTimeStamp'], 'm-o', markersize=4)
    axes[1, 1].set_xlabel('数据包编号')
    axes[1, 1].set_ylabel('时间戳')
    axes[1, 1].set_title('时间戳变化')
    axes[1, 1].grid(True)
    
    plt.tight_layout()
    plt.savefig('./data/gnss_analysis_plots.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"可视化图表已保存到: ./data/gnss_analysis_plots.png")

def main():
    """主函数"""
    # 确保输出目录存在
    os.makedirs('./data', exist_ok=True)
    
    # 分析当前目录中的WAV文件
    wav_file_path = "./0053_20250723_202435.part1.iq.wav"
    
    if os.path.exists(wav_file_path):
        analyze_gnss_data(wav_file_path)
    else:
        print(f"WAV文件不存在: {wav_file_path}")
        print("请检查文件路径")

if __name__ == "__main__":
    main()
