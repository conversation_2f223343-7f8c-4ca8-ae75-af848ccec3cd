#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script to examine msgpack data structure
"""

import struct
import msgpack

def debug_msgpack_data():
    """Debug the msgpack configuration data"""
    file_path = "./data/0053_20250805_172344.part1.spectrum"
    
    try:
        with open(file_path, 'rb') as file:
            # Read Length at offset 72 + 10MB
            file.seek(72 + 10 * 1024 * 1024)
            length_bytes = file.read(2)
            length = struct.unpack('>H', length_bytes)[0]  # Big-endian uint16
            length = ((length & 0xff) << 8 | (length & 0xff00) >> 8)  # Swap bytes
            
            print(f"Configuration data length: {length} bytes")
            
            # Read configuration data
            config_data = file.read(length)
            print(f"Actually read: {len(config_data)} bytes")
            
            # Try to unpack all items and see their types
            unpacker = msgpack.Unpacker(raw=False)
            unpacker.feed(config_data)
            
            items = []
            try:
                while True:
                    item = next(unpacker)
                    items.append(item)
            except StopIteration:
                pass
            
            print(f"Total msgpack items found: {len(items)}")
            
            # Print first 20 items with their types
            for i, item in enumerate(items[:20]):
                print(f"Item {i:2d}: {type(item).__name__:10s} = {item}")
            
            if len(items) > 20:
                print("...")
                # Print last 10 items
                for i, item in enumerate(items[-10:], len(items)-10):
                    print(f"Item {i:2d}: {type(item).__name__:10s} = {item}")
                    
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    debug_msgpack_data()
