# GNSS地图生成器使用说明

## 概述

这个地图生成器可以从WAV文件中提取GNSS坐标信息，并生成对应位置的高德地图卫星图像。

## 功能特点

- 自动从WAV文件中提取GNSS经纬度信息
- 支持WGS-84到GCJ-02坐标系转换
- 生成高清卫星地图图像
- 支持交互式和命令行两种使用模式
- 自动在地图上标记位置点

## 文件说明

- `map.py` - 主程序文件
- `main.py` - GNSS数据提取模块
- `WGS84_GCJ02.py` - 坐标系转换模块
- `htra_api.py` - 硬件API接口

## 使用方法

### 1. 交互式模式

直接运行程序：
```bash
python map.py
```

程序会：
1. 自动扫描当前目录中的WAV文件
2. 让用户选择要处理的文件
3. 询问地图缩放级别
4. 生成地图并保存

### 2. 命令行模式

```bash
python map.py <WAV文件路径> [缩放级别] [输出文件名]
```

参数说明：
- `WAV文件路径`: 必需，要处理的WAV文件
- `缩放级别`: 可选，1-20之间的数字，默认18
- `输出文件名`: 可选，输出图片文件名

示例：
```bash
# 使用默认缩放级别18
python map.py 0053_20250725_083011.part1.iq.wav

# 指定缩放级别15
python map.py 0053_20250725_083011.part1.iq.wav 15

# 指定输出文件名
python map.py 0053_20250725_083011.part1.iq.wav 15 my_map.png
```

## 输出文件

- 地图图像文件（PNG格式）
- 文件名格式：`map_<WAV文件名>.png`
- 图像尺寸：1920x1080像素
- 地图类型：高德卫星地图

## 坐标信息

程序会显示以下信息：
- 原始坐标（WGS-84坐标系）
- 转换后坐标（GCJ-02坐标系）
- 时间信息（北京时间）
- 地图缩放级别和尺寸

## 注意事项

1. 需要有效的高德地图API密钥
2. 确保WAV文件包含有效的GNSS数据
3. 需要网络连接来获取地图图像
4. 如果WAV文件不存在或无法读取，会使用默认坐标

## 依赖库

- requests - HTTP请求
- PIL (Pillow) - 图像处理
- numpy - 数值计算
- matplotlib - 绘图（main.py中使用）

## 错误处理

- 文件不存在：使用默认坐标
- 网络错误：显示错误信息
- 坐标解析失败：使用默认坐标
- API调用失败：显示HTTP状态码和错误信息

## 示例输出

```
=== 基于GNSS数据的交互式地图生成器 ===
找到 4 个WAV文件:
  1. 0053_20250723_202435.part1.iq.wav
  2. 0053_20250724_100322.part1.iq.wav
  3. 0053_20250724_165611.part1.iq.wav
  4. 0053_20250725_083011.part1.iq.wav

请选择WAV文件 (1-4) 或按回车使用最新文件: 
使用最新文件: 0053_20250725_083011.part1.iq.wav
请输入缩放级别 (1-20, 默认18): 
正在从 0053_20250725_083011.part1.iq.wav 提取GNSS信息...
从WAV文件获取到坐标: 经度=112.995010, 纬度=28.229507
时间信息: 2025-07-25 08:30:09.000000 CST
原始坐标 (WGS-84): 经度=112.995010, 纬度=28.229507
转换后坐标 (GCJ-02): 经度=113.000636, 纬度=28.226141
地图缩放级别: 18
地图尺寸: 1920x1080
正在获取地图图片...
地图图片已保存到: map_0053_20250725_083011.part1.iq.png
地图图片已在默认图片查看器中打开

地图生成成功！
```
