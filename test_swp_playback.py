#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for SWP Mode PlayBack Python implementation
"""

import os
import struct
from swp_mode_playback import (
    swap_bytes_uint64, swap_bytes_uint32, swap_bytes_uint16,
    swap_bytes_float, swap_bytes_double,
    SWP_Profile_TypeDef, SWP_TraceInfo_TypeDef, MeasAuxInfo_TypeDef
)


def test_byte_swapping():
    """Test byte swapping functions"""
    print("Testing byte swapping functions...")
    
    # Test uint64
    test_val_64 = 0x0123456789ABCDEF
    swapped_64 = swap_bytes_uint64(test_val_64)
    print(f"uint64: 0x{test_val_64:016X} -> 0x{swapped_64:016X}")
    
    # Test uint32
    test_val_32 = 0x01234567
    swapped_32 = swap_bytes_uint32(test_val_32)
    print(f"uint32: 0x{test_val_32:08X} -> 0x{swapped_32:08X}")
    
    # Test uint16
    test_val_16 = 0x0123
    swapped_16 = swap_bytes_uint16(test_val_16)
    print(f"uint16: 0x{test_val_16:04X} -> 0x{swapped_16:04X}")
    
    # Test float
    test_float = 3.14159
    swapped_float = swap_bytes_float(test_float)
    print(f"float: {test_float} -> {swapped_float}")
    
    # Test double
    test_double = 3.141592653589793
    swapped_double = swap_bytes_double(test_double)
    print(f"double: {test_double} -> {swapped_double}")
    
    print("Byte swapping tests completed.\n")


def test_data_structures():
    """Test data structure creation and initialization"""
    print("Testing data structures...")
    
    # Test SWP_Profile_TypeDef
    profile = SWP_Profile_TypeDef()
    profile.StartFreq_Hz = 1000000.0
    profile.StopFreq_Hz = 2000000.0
    profile.CenterFreq_Hz = 1500000.0
    print(f"Profile - Start: {profile.StartFreq_Hz}, Stop: {profile.StopFreq_Hz}, Center: {profile.CenterFreq_Hz}")
    
    # Test SWP_TraceInfo_TypeDef
    trace_info = SWP_TraceInfo_TypeDef()
    trace_info.FullsweepTracePoints = 1024
    trace_info.PartialsweepTracePoints = 256
    trace_info.TotalHops = 4
    print(f"Trace Info - Full: {trace_info.FullsweepTracePoints}, Partial: {trace_info.PartialsweepTracePoints}, Hops: {trace_info.TotalHops}")
    
    # Test MeasAuxInfo_TypeDef
    aux_info = MeasAuxInfo_TypeDef()
    aux_info.MaxPower_dBm = -30.5
    aux_info.Temperature = 25.0
    aux_info.Latitude = 40.7128
    aux_info.Longitude = -74.0060
    print(f"Aux Info - Power: {aux_info.MaxPower_dBm} dBm, Temp: {aux_info.Temperature}°C")
    print(f"GPS: {aux_info.Latitude}, {aux_info.Longitude}")
    
    print("Data structure tests completed.\n")


def test_file_operations():
    """Test basic file operations"""
    print("Testing file operations...")
    
    # Create test data directory
    test_dir = "./test_data/"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
        print(f"Created test directory: {test_dir}")
    
    # Create a simple test file
    test_file = os.path.join(test_dir, "test_output.txt")
    test_data = [
        (1000000.0, -50.5),
        (1001000.0, -51.2),
        (1002000.0, -49.8),
        (1003000.0, -52.1)
    ]
    
    try:
        with open(test_file, 'w') as f:
            for freq, power in test_data:
                f.write(f"{freq}\t{power}\n")
        print(f"Test file created: {test_file}")
        
        # Read back and verify
        with open(test_file, 'r') as f:
            lines = f.readlines()
            print(f"Read {len(lines)} lines from test file")
            for i, line in enumerate(lines[:2]):  # Show first 2 lines
                print(f"  Line {i+1}: {line.strip()}")
        
        # Clean up
        os.remove(test_file)
        os.rmdir(test_dir)
        print("Test file and directory cleaned up")
        
    except Exception as e:
        print(f"File operation error: {e}")
    
    print("File operation tests completed.\n")


def test_struct_packing():
    """Test struct packing/unpacking similar to C++ binary operations"""
    print("Testing struct packing/unpacking...")
    
    # Test packing and unpacking various data types
    test_values = {
        'uint64': 0x123456789ABCDEF0,
        'uint32': 0x12345678,
        'uint16': 0x1234,
        'float': 3.14159,
        'double': 2.718281828459045
    }
    
    for data_type, value in test_values.items():
        if data_type == 'uint64':
            packed = struct.pack('>Q', value)  # Big-endian
            unpacked = struct.unpack('>Q', packed)[0]
        elif data_type == 'uint32':
            packed = struct.pack('>I', value)
            unpacked = struct.unpack('>I', packed)[0]
        elif data_type == 'uint16':
            packed = struct.pack('>H', value)
            unpacked = struct.unpack('>H', packed)[0]
        elif data_type == 'float':
            packed = struct.pack('>f', value)
            unpacked = struct.unpack('>f', packed)[0]
        elif data_type == 'double':
            packed = struct.pack('>d', value)
            unpacked = struct.unpack('>d', packed)[0]
        
        print(f"{data_type}: {value} -> packed -> {unpacked}")
        if isinstance(value, float):
            # For floating point, check relative error
            if abs(value) > 1e-10:
                assert abs((value - unpacked) / value) < 1e-6
            else:
                assert abs(value - unpacked) < 1e-10
        else:
            assert value == unpacked
    
    print("Struct packing tests completed.\n")


def main():
    """Run all tests"""
    print("SWP Mode PlayBack - Python Implementation Tests")
    print("=" * 60)
    
    try:
        test_byte_swapping()
        test_data_structures()
        test_file_operations()
        test_struct_packing()
        
        print("All tests completed successfully!")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
