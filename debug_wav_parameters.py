"""
调试WAV文件参数解析
分析msgpack数据中的中心频率和采样率等参数
"""

import struct
import msgpack
import os
from integrated_iq_map_analyzer import find_wav_files

def swap_16(val):
    return ((val & 0x00ff) << 8) | ((val & 0xff00) >> 8)

def debug_wav_parameters(wav_file_path):
    """详细分析WAV文件中的参数"""
    print(f"\n=== 分析文件: {wav_file_path} ===")
    
    try:
        with open(wav_file_path, 'rb') as file:
            # 读取IQS_Profile和IQS_StreamInfo结构体大小
            file.seek(108)
            struct_size_bytes = file.read(2)
            struct_size = swap_16(struct.unpack('<H', struct_size_bytes)[0])
            print(f"结构体大小: {struct_size} 字节")
            
            # 读取IQS_Profile和IQS_StreamInfo数据
            iqs_profile_stream_data = file.read(struct_size)
            print(f"读取数据长度: {len(iqs_profile_stream_data)} 字节")
            
            # 使用msgpack解析数据
            unpacker = msgpack.Unpacker()
            unpacker.feed(iqs_profile_stream_data)
            unpacked_data = list(unpacker)
            
            print(f"解析出 {len(unpacked_data)} 个参数:")
            print("-" * 60)
            
            # 显示前50个参数的详细信息
            for i in range(min(50, len(unpacked_data))):
                value = unpacked_data[i]
                value_type = type(value).__name__
                
                # 尝试解释这个值可能代表什么
                interpretation = interpret_value(i, value)
                
                print(f"[{i:2d}] {value_type:8s} {value:15} {interpretation}")
            
            if len(unpacked_data) > 50:
                print(f"... 还有 {len(unpacked_data) - 50} 个参数")
            
            print("-" * 60)
            
            # 重点分析关键参数
            analyze_key_parameters(unpacked_data)
            
    except Exception as e:
        print(f"分析文件时出错: {e}")

def interpret_value(index, value):
    """尝试解释参数值的含义"""
    interpretations = {
        0: "中心频率 (Hz)",
        1: "参考电平 (dBm)", 
        2: "抽取因子",
        37: "带宽 (Hz)",
        38: "IQ采样率 (Hz)",
        39: "可能的采样率备用值"
    }
    
    base_interpretation = interpretations.get(index, "未知参数")
    
    # 根据值的大小和类型进行进一步解释
    if isinstance(value, (int, float)):
        if value == 0:
            return f"{base_interpretation} (值为0)"
        elif 1e6 <= value <= 1e12:  # 可能是频率
            return f"{base_interpretation} -> {value/1e6:.1f} MHz"
        elif 1e3 <= value <= 1e6:  # 可能是kHz
            return f"{base_interpretation} -> {value/1e3:.1f} kHz"
        elif 0 < value < 1000:  # 可能是其他单位
            return f"{base_interpretation} -> {value}"
        else:
            return f"{base_interpretation} -> {value}"
    else:
        return f"{base_interpretation} -> {value}"

def analyze_key_parameters(unpacked_data):
    """分析关键参数"""
    print("关键参数分析:")
    print("=" * 60)
    
    # 中心频率 (索引0)
    if len(unpacked_data) > 0:
        center_freq = unpacked_data[0]
        print(f"中心频率 [0]: {center_freq}")
        if isinstance(center_freq, (int, float)):
            print(f"  -> {center_freq/1e6:.1f} MHz")
            print(f"  -> {center_freq/1e9:.3f} GHz")
    
    # 参考电平 (索引1)
    if len(unpacked_data) > 1:
        ref_level = unpacked_data[1]
        print(f"参考电平 [1]: {ref_level} dBm")
    
    # 抽取因子 (索引2)
    if len(unpacked_data) > 2:
        decimate_factor = unpacked_data[2]
        print(f"抽取因子 [2]: {decimate_factor}")
    
    # 带宽 (索引37)
    if len(unpacked_data) > 37:
        bandwidth = unpacked_data[37]
        print(f"带宽 [37]: {bandwidth}")
        if isinstance(bandwidth, (int, float)) and bandwidth > 0:
            print(f"  -> {bandwidth/1e6:.1f} MHz")
    
    # IQ采样率 (索引38)
    if len(unpacked_data) > 38:
        sample_rate = unpacked_data[38]
        print(f"IQ采样率 [38]: {sample_rate}")
        if isinstance(sample_rate, (int, float)):
            if sample_rate > 0:
                print(f"  -> {sample_rate/1e6:.1f} MHz")
                print(f"  -> {sample_rate/1e3:.1f} kHz")
                print(f"  -> {sample_rate} Hz")
            else:
                print("  -> 值为0，可能需要从其他位置读取")
    
    # 检查其他可能的采样率位置
    print("\n其他可能的采样率位置:")
    for idx in [36, 37, 39, 40, 41, 42]:
        if len(unpacked_data) > idx:
            value = unpacked_data[idx]
            if isinstance(value, (int, float)) and value > 0:
                print(f"  [{idx}]: {value} -> {value/1e6:.1f} MHz")

def compare_with_main_py():
    """与main.py的解析逻辑对比"""
    print("\n" + "=" * 80)
    print("与main.py解析逻辑对比:")
    print("=" * 80)
    
    print("main.py的解析逻辑:")
    print("  - 中心频率: unpacked_data[0]")
    print("  - 参考电平: unpacked_data[1]") 
    print("  - 抽取因子: unpacked_data[2]")
    print("  - 带宽: unpacked_data[37]")
    print("  - IQ采样率: unpacked_data[38]")
    print("  - 如果采样率 < 1.0，则乘以1000000 (假设是MHz)")
    
    print("\nintegrated_iq_map_analyzer.py的解析逻辑:")
    print("  - 中心频率: unpacked_data[0]")
    print("  - 采样率: 尝试索引 [38, 39, 37, 36]")
    print("  - 单位转换: < 1.0 -> *1000000, < 1000 -> *1000")

def main():
    """主函数"""
    print("🔍 WAV文件参数调试分析")
    print("=" * 80)
    
    # 查找WAV文件
    wav_files = find_wav_files()
    
    if not wav_files:
        print("当前目录中没有找到WAV文件")
        return
    
    print(f"找到 {len(wav_files)} 个WAV文件:")
    for i, file in enumerate(wav_files, 1):
        print(f"  {i}. {file}")
    
    # 分析每个文件
    for wav_file in wav_files:
        debug_wav_parameters(wav_file)
    
    # 对比解析逻辑
    compare_with_main_py()
    
    print("\n" + "=" * 80)
    print("🎯 分析总结:")
    print("1. 检查中心频率和采样率的实际值")
    print("2. 验证单位转换是否正确")
    print("3. 确认参数索引位置是否准确")
    print("4. 对比不同解析方法的结果")

if __name__ == "__main__":
    main()
