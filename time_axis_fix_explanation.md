# 时频图时间轴负数问题解决方案

## 问题描述

在使用短时傅里叶变换(STFT)生成时频图时，时间坐标轴出现负数值，这会让用户感到困惑，因为通常期望时间从0开始。

## 问题原因分析

### 1. STFT算法特性
短时傅里叶变换在计算时间轴时，默认行为可能导致负数时间：

```python
f, t, Zxx = signal.stft(complex_signal, fs=sample_rate, ...)
# t数组可能包含负数值
```

### 2. 窗函数影响
- **窗长度(nperseg)**：窗函数的长度影响第一个时间点的位置
- **重叠(noverlap)**：窗之间的重叠也会影响时间轴的起始点
- **边界处理**：STFT在信号边界的处理方式

### 3. 采样率问题
如果采样率读取不正确（如为0或很小的值），会导致时间轴计算异常。

## 解决方案

### 方案1：时间轴偏移校正（已实现）

```python
def generate_spectrogram(self, iq_data, title="Spectrogram"):
    # ... STFT计算 ...
    f, t, Zxx = signal.stft(complex_signal, ...)
    
    # 关键修复：调整时间轴，确保从0开始
    t_adjusted = t - t[0]  # 将时间轴调整为从0开始
    t_ms = t_adjusted * 1000  # 转换为毫秒
    
    # 绘制时使用调整后的时间轴
    im = ax.pcolormesh(t_ms, f_offset_mhz, power_db, ...)
    ax.set_xlim(0, t_ms[-1])  # 确保显示范围从0开始
```

### 方案2：采样率修复（已实现）

```python
# 确保采样率有效
if self.sample_rate is None or self.sample_rate <= 0:
    self.sample_rate = 10e6  # 默认10MHz采样率
    print(f"使用默认采样率: {self.sample_rate/1e6:.1f} MHz")

# 改进的采样率读取逻辑
for idx in [38, 39, 37, 36]:  # 尝试不同的索引位置
    if len(unpacked_data) > idx:
        potential_rate = unpacked_data[idx]
        if potential_rate > 0:
            sample_rate = potential_rate
            break
```

### 方案3：STFT参数优化（已实现）

```python
# 确保窗长度合理
nperseg = min(256, len(complex_signal) // 4)
noverlap = nperseg // 2  # 50%重叠

# 显式设置参数避免警告
f, t, Zxx = signal.stft(complex_signal, 
                       fs=self.sample_rate, 
                       window='hann', 
                       nperseg=nperseg, 
                       noverlap=noverlap,
                       return_onesided=False)
```

## 修复效果对比

### 修复前：
```
原始时间轴范围: -0.128 到 1.536 ms  # 包含负数
```

### 修复后：
```
修正后时间轴范围: 0.00 到 1.664 ms   # 从0开始
时频图参数: 时间范围 0-1.6ms, 频率范围 -5.0-5.0MHz
```

## 技术细节

### 1. 时间轴计算原理
STFT的时间轴计算公式：
```
t[k] = k * hop_length / sample_rate
```
其中：
- `k`：时间窗索引
- `hop_length = nperseg - noverlap`：跳跃长度
- `sample_rate`：采样率

### 2. 窗函数中心对齐
STFT默认将窗函数中心对齐到时间点，这可能导致：
- 第一个窗的中心不在t=0
- 时间轴起始点为负数

### 3. 边界效应
信号开始和结束处的边界效应也会影响时间轴的计算。

## 验证方法

### 1. 合成信号测试
```python
# 生成已知参数的测试信号
fs = 10e6  # 10MHz采样率
t_signal = np.arange(0, 0.001, 1/fs)  # 1ms信号
signal_complex = np.exp(1j * 2 * np.pi * 1e6 * t_signal)  # 1MHz正弦波
```

### 2. 时间轴检查
```python
print(f"原始时间轴: {t[0]*1000:.2f} 到 {t[-1]*1000:.2f} ms")
t_adjusted = t - t[0]
print(f"修正后时间轴: {t_adjusted[0]*1000:.2f} 到 {t_adjusted[-1]*1000:.2f} ms")
```

## 最佳实践

### 1. 参数选择
- **nperseg**：选择2的幂次，如256、512
- **noverlap**：通常选择nperseg的50%-75%
- **window**：'hann'窗是常用选择

### 2. 时间轴处理
- 总是检查时间轴的起始点
- 如果需要从0开始，进行偏移校正
- 设置合适的显示范围

### 3. 采样率验证
- 验证采样率的合理性
- 提供默认值作为备用
- 显示实际使用的参数

## 测试结果

运行测试脚本 `test_spectrogram_time_axis.py` 的结果：

✅ **时间轴修复**：从负数修正为从0开始  
✅ **采样率检测**：改进了从WAV文件读取采样率的逻辑  
✅ **参数优化**：自动调整STFT参数以获得最佳效果  
✅ **可视化对比**：生成对比图显示修复效果  

## 生成的测试文件

1. **time_axis_comparison.png**：修复前后的时间轴对比
2. **synthetic_signal_analysis.png**：合成信号的完整分析
3. **test_fixed_time.png**：修复后的实际WAV文件分析结果

## 总结

通过以上修复，成功解决了时频图时间轴负数的问题：

1. **根本原因**：STFT算法的默认时间轴计算方式
2. **解决方法**：时间轴偏移校正 + 采样率修复 + 参数优化
3. **效果验证**：时间轴现在从0开始，符合用户期望
4. **用户体验**：提供清晰的时间和频率信息显示

这个修复确保了时频图的时间轴始终从0开始，提供了更直观和用户友好的可视化效果。
