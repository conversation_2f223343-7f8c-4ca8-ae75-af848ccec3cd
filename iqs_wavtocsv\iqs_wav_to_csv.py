#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IQS模式WAV文件转CSV文件的Python实现
支持批量处理指定文件夹下所有子文件夹中的WAV文件
"""

import os
import struct
import msgpack
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Tuple, List
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IQSWavConverter:
    """IQS WAV文件转换器"""
    
    def __init__(self):
        self.data_format_map = {
            0: 4,  # Complex32bit
            1: 2,  # Complex16bit  
            2: 1   # Complex8bit
        }
    
    def swap_bytes_16(self, value: int) -> int:
        """16位字节序转换"""
        return ((value & 0x00ff) << 8) | ((value & 0xff00) >> 8)
    
    def swap_bytes_32(self, value: int) -> int:
        """32位字节序转换"""
        return (((value & 0xff000000) >> 24) |
                ((value & 0x00ff0000) >> 8) |
                ((value & 0x0000ff00) << 8) |
                ((value & 0x000000ff) << 24))
    
    def swap_bytes_64(self, value: int) -> int:
        """64位字节序转换"""
        result = 0
        result |= (value & 0xff00000000000000) >> 56
        result |= (value & 0x00ff000000000000) >> 40
        result |= (value & 0x0000ff0000000000) >> 24
        result |= (value & 0x000000ff00000000) >> 8
        result |= (value & 0x00000000ff000000) << 8
        result |= (value & 0x0000000000ff0000) << 24
        result |= (value & 0x000000000000ff00) << 40
        result |= (value & 0x00000000000000ff) << 56
        return result
    
    def convert_endian_float(self, value: float) -> float:
        """float类型字节序转换"""
        # 将float转为bytes，然后反转字节序，再转回float
        bytes_data = struct.pack('>f', value)  # 大端序
        return struct.unpack('<f', bytes_data)[0]  # 小端序
    
    def convert_endian_double(self, value: float) -> float:
        """double类型字节序转换"""
        # 将double转为bytes，然后反转字节序，再转回double
        bytes_data = struct.pack('>d', value)  # 大端序
        return struct.unpack('<d', bytes_data)[0]  # 小端序
    
    def get_iqs_wav_file_info(self, file_path: str) -> Tuple[Dict[str, Any], Dict[str, Any], Dict[str, Any], int]:
        """
        获取IQS WAV文件信息
        返回: (IQS_Profile, IQS_StreamInfo, DeviceInfo, PacketCount)
        """
        try:
            with open(file_path, 'rb') as file:
                # 读取IQS_Profile和IQS_StreamInfo结构体字节长度
                file.seek(108)
                struct_size_bytes = file.read(2)
                struct_size = struct.unpack('<H', struct_size_bytes)[0]
                
                # 读取IQS_Profile和IQS_StreamInfo数据
                iqs_data = file.read(struct_size)
                
                # 使用msgpack解析数据
                unpacker = msgpack.Unpacker(raw=False)
                unpacker.feed(iqs_data)
                
                # 解析IQS_Profile
                iqs_profile = {}
                iqs_stream_info = {}
                
                # 按照C++代码的顺序解析各个字段
                fields = [
                    'CenterFreq_Hz', 'RefLevel_dBm', 'DecimateFactor', 'RxPort',
                    'BusTimeout_ms', 'TriggerSource', 'TriggerEdge', 'TriggerMode',
                    'TriggerLength', 'TriggerOutMode', 'TriggerOutPulsePolarity',
                    'TriggerLevel_dBm', 'TriggerLevel_SafeTime', 'TriggerDelay',
                    'PreTriggerTime', 'TriggerTimerSync', 'TriggerTimer_Period',
                    'EnableReTrigger', 'ReTrigger_Period', 'ReTrigger_Count',
                    'DataFormat', 'GainStrategy', 'Preamplifier', 'AnalogIFBWGrade',
                    'IFGainGrade', 'EnableDebugMode', 'ReferenceClockSource',
                    'ReferenceClockFrequency', 'EnableReferenceClockOut',
                    'SystemClockSource', 'ExternalSystemClockFrequency',
                    'NativeIQSampleRate_SPS', 'EnableIFAGC', 'Atten',
                    'DCCancelerMode', 'QDCMode', 'QDCIGain', 'QDCQGain',
                    'QDCPhaseComp', 'DCCIOffset', 'DCCQOffset', 'LOOptimization'
                ]
                
                stream_fields = [
                    'Bandwidth', 'IQSampleRate', 'PacketCount', 'StreamSamples',
                    'StreamDataSize', 'PacketSamples', 'PacketDataSize', 'GainParameter'
                ]
                
                # 解析IQS_Profile字段
                for field in fields:
                    try:
                        value = next(unpacker)
                        iqs_profile[field] = value
                    except StopIteration:
                        break
                
                # 解析IQS_StreamInfo字段
                for field in stream_fields:
                    try:
                        value = next(unpacker)
                        iqs_stream_info[field] = value
                    except StopIteration:
                        break
                
                # 读取DeviceInfo
                device_info = {}
                device_info_size_bytes = file.read(2)
                
                # 读取设备UID
                device_uid_bytes = file.read(8)
                device_uid = struct.unpack('<Q', device_uid_bytes)[0]
                # 应用字节序转换，与C++中的swap_641函数相同
                device_uid = self.swap_bytes_64(device_uid)
                device_info['DeviceUID'] = device_uid
                
                # 读取设备型号
                model_bytes = file.read(2)
                model = struct.unpack('<H', model_bytes)[0]
                model = self.swap_bytes_16(model)
                device_info['Model'] = model
                
                # 读取硬件版本
                hw_version_bytes = file.read(2)
                hw_version = struct.unpack('<H', hw_version_bytes)[0]
                hw_version = self.swap_bytes_16(hw_version)
                device_info['HardwareVersion'] = hw_version
                
                # 读取MCU固件版本
                mfw_version_bytes = file.read(4)
                mfw_version = struct.unpack('<I', mfw_version_bytes)[0]
                mfw_version = self.swap_bytes_32(mfw_version)
                device_info['MFWVersion'] = mfw_version
                
                # 读取FPGA固件版本
                ffw_version_bytes = file.read(4)
                ffw_version = struct.unpack('<I', ffw_version_bytes)[0]
                ffw_version = self.swap_bytes_32(ffw_version)
                device_info['FFWVersion'] = ffw_version
                
                # 获取IQ数据总长度
                file.seek(25 * 1024 * 1024 + 404)
                iq_size_bytes = file.read(4)
                iq_size = struct.unpack('<I', iq_size_bytes)[0]
                
                packet_count = iq_size // 64968  # 每个包64968字节
                
                return iqs_profile, iqs_stream_info, device_info, packet_count
                
        except Exception as e:
            logger.error(f"读取文件信息失败 {file_path}: {e}")
            raise
    
    def get_iqs_wav_file_data(self, iqs_profile: Dict[str, Any], file_path: str, 
                             packet_num: int) -> List[int]:
        """获取指定包的IQ数据"""
        try:
            with open(file_path, 'rb') as file:
                # 确定数据格式
                data_format = iqs_profile.get('DataFormat', 1)
                format_size = self.data_format_map.get(data_format, 2)
                
                # 定位到指定包的数据位置
                file.seek(25 * 1024 * 1024 + 408 + packet_num * 64968)
                
                # 读取IQ数据
                iq_data = []
                samples_per_packet = 64968 // format_size
                
                for i in range(samples_per_packet):
                    '''data_bytes = file.read(2)  # 读取16位数据
                    if len(data_bytes) < 2:
                        break
                    value = struct.unpack('<h', data_bytes)[0]  # 有符号16位整数
                    iq_data.append(value)'''
                    q_bytes = file.read(2)
                    i_bytes = file.read(2)
                    q_val = struct.unpack('<h', q_bytes)[0]
                    i_val = struct.unpack('<h', i_bytes)[0]
                     # 存储为(Q, I)对，与C++保持一致
                    iq_data.append((q_val, i_val))
                    
                return iq_data
                
        except Exception as e:
            logger.error(f"读取包数据失败 {file_path}, 包号 {packet_num}: {e}")
            raise
    
    def convert_wav_to_csv(self, wav_file_path: str, output_dir: str = None) -> str:
        """
        将单个WAV文件转换为CSV文件
        返回输出CSV文件路径
        """
        try:
            logger.info(f"开始转换文件: {wav_file_path}")
            
            # 获取文件信息
            iqs_profile, iqs_stream_info, device_info, packet_count = self.get_iqs_wav_file_info(wav_file_path)
            
            # 确定输出路径
            if output_dir is None:
                output_dir = os.path.join(os.path.dirname(wav_file_path), "data")
            
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(wav_file_path))[0]
            csv_file_path = os.path.join(output_dir, f"{base_name}_IQSMode_Data.csv")
            
            # 创建单独的I和Q数组，与main.py实现保持一致
            i_data = []
            q_data = []
            
            # 打开CSV文件进行写入
            with open(csv_file_path, 'w', encoding='utf-8') as csv_file:
                # 写入头部信息
                csv_file.write(f"Device UID:,{device_info['DeviceUID']:x}\n")
                csv_file.write(f"CenterFreq_Hz:,{iqs_profile['CenterFreq_Hz']}\n")
                csv_file.write(f"IQSampleRate:,{iqs_stream_info['IQSampleRate']}\n")
                csv_file.write(f"DecimateFactor:,{iqs_profile['DecimateFactor']}\n")
                csv_file.write("I_Data,Q_Data\n")
                
                # 处理每个数据包
                for packet_num in range(packet_count):
                    logger.info(f"处理包 {packet_num + 1}/{packet_count}")
                    
                    iq_pairs = self.get_iqs_wav_file_data(iqs_profile, wav_file_path, packet_num)
                    
                    # 确定当前包的样本数
                    points = iqs_stream_info.get('PacketSamples', len(iq_pairs))
                    if (packet_num == packet_count - 1 and 
                        'StreamSamples' in iqs_stream_info and 
                        'PacketSamples' in iqs_stream_info and
                        iqs_stream_info['StreamSamples'] % iqs_stream_info['PacketSamples'] != 0):
                        points = iqs_stream_info['StreamSamples'] % iqs_stream_info['PacketSamples']
                    
                    # 提取I和Q数据并写入CSV
                    for idx, (q_val, i_val) in enumerate(iq_pairs):
                        if idx < points:
                            # 添加到单独的数组中
                            q_data.append(q_val)
                            i_data.append(i_val)
                            # 直接写入CSV文件，I在前Q在后
                            csv_file.write(f"{i_val},{q_val}\n")
            
            logger.info(f"转换完成: {csv_file_path}")
            return csv_file_path
            
        except Exception as e:
            logger.error(f"转换文件失败 {wav_file_path}: {e}")
            raise
    
    def batch_convert_directory(self, root_dir: str, output_base_dir: str = None) -> List[str]:
        """
        批量转换指定目录下所有子文件夹中的WAV文件
        """
        converted_files = []
        root_path = Path(root_dir)
        
        if not root_path.exists():
            logger.error(f"目录不存在: {root_dir}")
            return converted_files
        
        # 查找所有WAV文件
        wav_files = list(root_path.rglob("*.wav"))
        logger.info(f"找到 {len(wav_files)} 个WAV文件")
        
        for wav_file in wav_files:
            try:
                # 确定输出目录
                if output_base_dir:
                    # 保持相对目录结构
                    rel_path = wav_file.parent.relative_to(root_path)
                    output_dir = os.path.join(output_base_dir, rel_path, "data")
                else:
                    output_dir = os.path.join(wav_file.parent, "data")
                
                # 转换文件
                csv_file = self.convert_wav_to_csv(str(wav_file), output_dir)
                converted_files.append(csv_file)
                
            except Exception as e:
                logger.error(f"转换文件失败 {wav_file}: {e}")
                continue
        
        logger.info(f"批量转换完成，成功转换 {len(converted_files)} 个文件")
        return converted_files


def main():
    """主函数"""
    try:
        # 加载配置
        from config import load_config, validate_config, print_config

        # 验证配置
        errors = validate_config()
        if errors:
            print("配置验证失败:")
            for error in errors:
                print(f"  ✗ {error}")
            print("\n请检查并修改 config.py 文件中的配置")
            return

        # 加载配置
        config = load_config()

        # 打印配置信息
        print_config()

        # 创建转换器实例
        converter = IQSWavConverter()

        # 批量转换
        converted_files = converter.batch_convert_directory(
            config['input_root_dir'],
            config['output_base_dir']
        )

        print(f"\n转换完成！共转换了 {len(converted_files)} 个文件:")
        for file_path in converted_files:
            print(f"  - {file_path}")

    except ImportError:
        # 如果没有配置文件，使用默认配置
        print("未找到配置文件，使用默认配置...")
        ROOT_DIR = r"D:\nxm-60\Windows\SAStudio4\data"  # 根目录路径
        OUTPUT_BASE_DIR = None  # 输出基础目录，None表示在原文件夹下创建data子文件夹

        # 创建转换器实例
        converter = IQSWavConverter()

        # 批量转换
        converted_files = converter.batch_convert_directory(ROOT_DIR, OUTPUT_BASE_DIR)
        print(f"\n转换完成！共转换了 {len(converted_files)} 个文件:")
        for file_path in converted_files:
            print(f"  - {file_path}")

    except Exception as e:
        logger.error(f"批量转换失败: {e}")


if __name__ == "__main__":
    main()



