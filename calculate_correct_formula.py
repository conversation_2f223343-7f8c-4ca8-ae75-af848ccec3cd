#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
计算正确的时间戳转换公式
"""

from datetime import datetime, timezone, timedelta

def calculate_conversion_formula():
    """
    基于已知的时间戳值和目标时间计算转换公式
    """
    # 已知数据
    timestamp_value = 990956329492035.0
    target_beijing_time = "2025-07-23 20:24:35"
    
    print(f"已知时间戳值: {timestamp_value}")
    print(f"目标北京时间: {target_beijing_time}")
    
    # 转换目标时间为UTC和Unix时间戳
    target_dt_beijing = datetime.strptime(target_beijing_time, "%Y-%m-%d %H:%M:%S")
    target_dt_utc = target_dt_beijing - timedelta(hours=8)  # 北京时间转UTC
    target_unix_timestamp = target_dt_utc.timestamp()
    
    print(f"目标UTC时间: {target_dt_utc.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print(f"目标Unix时间戳: {target_unix_timestamp}")
    
    print("\n计算转换公式:")
    print("-" * 50)
    
    # 方法1: 直接比例计算
    ratio = target_unix_timestamp / timestamp_value
    print(f"比例系数: {ratio:.15e}")
    
    # 方法2: 假设是从某个epoch开始的计数
    # 尝试不同的时间单位
    time_units = [
        ("秒", 1),
        ("毫秒", 1000),
        ("微秒", 1000000),
        ("纳秒", 1000000000),
        ("100纳秒", 10000000),
    ]
    
    print(f"\n尝试不同的epoch和单位组合:")
    print("-" * 80)
    
    # 可能的epoch时间
    possible_epochs = [
        ("Unix Epoch (1970-01-01)", datetime(1970, 1, 1)),
        ("设备可能的制造时间 (2020-01-01)", datetime(2020, 1, 1)),
        ("设备可能的制造时间 (2021-01-01)", datetime(2021, 1, 1)),
        ("设备可能的制造时间 (2022-01-01)", datetime(2022, 1, 1)),
        ("设备可能的制造时间 (2023-01-01)", datetime(2023, 1, 1)),
        ("设备可能的制造时间 (2024-01-01)", datetime(2024, 1, 1)),
    ]
    
    best_matches = []
    
    for epoch_name, epoch_dt in possible_epochs:
        try:
            epoch_timestamp = epoch_dt.timestamp()
            
            for unit_name, divisor in time_units:
                # 计算: epoch + (timestamp_value / divisor) = target
                # 所以: epoch = target - (timestamp_value / divisor)
                calculated_epoch = target_unix_timestamp - (timestamp_value / divisor)
                calculated_epoch_dt = datetime.fromtimestamp(calculated_epoch, tz=timezone.utc)
                
                # 检查这个epoch是否合理（在1980-2025年之间）
                if 1980 <= calculated_epoch_dt.year <= 2025:
                    # 验证公式
                    verify_timestamp = calculated_epoch + (timestamp_value / divisor)
                    verify_dt = datetime.fromtimestamp(verify_timestamp, tz=timezone.utc)
                    verify_beijing = verify_dt + timedelta(hours=8)
                    
                    diff = abs(verify_timestamp - target_unix_timestamp)
                    
                    match = {
                        'epoch_name': epoch_name,
                        'unit': unit_name,
                        'calculated_epoch': calculated_epoch_dt,
                        'verify_utc': verify_dt,
                        'verify_beijing': verify_beijing,
                        'diff_seconds': diff
                    }
                    best_matches.append(match)
                    
                    print(f"{epoch_name:30} + {unit_name:8}: "
                          f"计算epoch = {calculated_epoch_dt.strftime('%Y-%m-%d %H:%M:%S UTC')}, "
                          f"验证 = {verify_beijing.strftime('%Y-%m-%d %H:%M:%S CST')}, "
                          f"差异 = {diff:.2f}秒")
        except:
            continue
    
    # 特殊情况：也许时间戳本身就包含了正确的信息，只是需要特殊的解释
    print(f"\n特殊解释尝试:")
    print("-" * 50)
    
    # 尝试将时间戳的不同部分解释为时间信息
    timestamp_int = int(timestamp_value)
    timestamp_hex = hex(timestamp_int)
    
    print(f"时间戳整数: {timestamp_int}")
    print(f"时间戳十六进制: {timestamp_hex}")
    print(f"时间戳二进制长度: {timestamp_int.bit_length()}位")
    
    # 尝试分解时间戳
    # 也许高位是日期，低位是时间
    if timestamp_int.bit_length() <= 64:
        # 32位分解
        high_32 = (timestamp_int >> 32) & 0xFFFFFFFF
        low_32 = timestamp_int & 0xFFFFFFFF
        
        print(f"\n32位分解:")
        print(f"高32位: {high_32} (0x{high_32:08X})")
        print(f"低32位: {low_32} (0x{low_32:08X})")
        
        # 检查是否有模式
        # 也许是YYYYMMDDHHMMSS格式的某种变体
        target_date_str = target_beijing_time.replace("-", "").replace(":", "").replace(" ", "")
        print(f"目标日期字符串: {target_date_str}")
        
        # 尝试在时间戳中找到日期模式
        timestamp_str = str(timestamp_int)
        if "20250723" in timestamp_str:
            print("*** 在时间戳中找到日期模式 20250723! ***")
        if "202435" in timestamp_str:
            print("*** 在时间戳中找到时间模式 202435! ***")
    
    # 显示最佳匹配
    if best_matches:
        best_matches.sort(key=lambda x: x['diff_seconds'])
        
        print(f"\n最佳匹配结果:")
        print("-" * 80)
        best = best_matches[0]
        print(f"最佳公式: {best['epoch_name']} + (时间戳值 / {best['unit']}对应的除数)")
        print(f"计算的epoch: {best['calculated_epoch'].strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print(f"验证UTC时间: {best['verify_utc'].strftime('%Y-%m-%d %H:%M:%S.%f UTC')}")
        print(f"验证北京时间: {best['verify_beijing'].strftime('%Y-%m-%d %H:%M:%S.%f CST')}")
        print(f"时间差异: {best['diff_seconds']:.6f}秒")
        
        if best['diff_seconds'] < 1:
            print("\n*** 找到了精确的转换公式! ***")
            return best
    
    return None

def main():
    """主函数"""
    print("=" * 80)
    print("计算正确的时间戳转换公式")
    print("=" * 80)
    
    result = calculate_conversion_formula()
    
    if result:
        print("\n" + "=" * 80)
        print("推荐使用的转换公式:")
        print("=" * 80)
        print(f"公式: {result['epoch_name']} + (timestamp_value / {result['unit']}除数)")
        print("这个公式可以用来更新main.py中的时间戳转换函数")

if __name__ == "__main__":
    main()
