#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IQS WAV转换器使用示例
"""

from iqs_wav_to_csv import IQSWavConverter
import os

def example_single_file():
    """示例：转换单个文件"""
    print("示例1: 转换单个文件")
    print("-" * 30)
    
    # 创建转换器实例
    converter = IQSWavConverter()
    
    # 指定要转换的WAV文件路径
    wav_file = r"D:\nxm-60\Windows\SAStudio4\data\BPSKQ2\0053_20250630_232013.part1.iq.wav"
    
    # 检查文件是否存在
    if not os.path.exists(wav_file):
        print(f"文件不存在: {wav_file}")
        print("请修改为实际存在的WAV文件路径")
        return
    
    try:
        # 转换文件
        csv_file = converter.convert_wav_to_csv(wav_file)
        print(f"转换成功: {csv_file}")
    except Exception as e:
        print(f"转换失败: {e}")

def example_batch_conversion():
    """示例：批量转换目录"""
    print("\n示例2: 批量转换目录")
    print("-" * 30)
    
    # 创建转换器实例
    converter = IQSWavConverter()
    
    # 指定要转换的根目录
    root_dir = r"D:\nxm-60\Windows\SAStudio4\data"
    
    # 检查目录是否存在
    if not os.path.exists(root_dir):
        print(f"目录不存在: {root_dir}")
        print("请修改为实际存在的目录路径")
        return
    
    try:
        # 批量转换
        converted_files = converter.batch_convert_directory(root_dir)
        print(f"批量转换完成，共转换 {len(converted_files)} 个文件")
        
        # 显示转换的文件列表
        if converted_files:
            print("\n转换的文件:")
            for i, file_path in enumerate(converted_files, 1):
                print(f"  {i}. {os.path.basename(file_path)}")
    except Exception as e:
        print(f"批量转换失败: {e}")

def example_custom_output_dir():
    """示例：指定自定义输出目录"""
    print("\n示例3: 指定自定义输出目录")
    print("-" * 30)
    
    # 创建转换器实例
    converter = IQSWavConverter()
    
    # 指定输入和输出目录
    input_dir = r"D:\nxm-60\Windows\SAStudio4\data"
    output_dir = r"D:\converted_csv_files"  # 自定义输出目录
    
    # 检查输入目录是否存在
    if not os.path.exists(input_dir):
        print(f"输入目录不存在: {input_dir}")
        return
    
    try:
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 批量转换到指定目录
        converted_files = converter.batch_convert_directory(input_dir, output_dir)
        print(f"转换完成，文件保存到: {output_dir}")
        print(f"共转换 {len(converted_files)} 个文件")
    except Exception as e:
        print(f"转换失败: {e}")

def example_get_file_info():
    """示例：获取WAV文件信息"""
    print("\n示例4: 获取WAV文件信息")
    print("-" * 30)
    
    # 创建转换器实例
    converter = IQSWavConverter()
    
    # 指定WAV文件
    wav_file = r"D:\nxm-60\Windows\SAStudio4\data\BPSKQ2\0053_20250630_232013.part1.iq.wav"
    
    if not os.path.exists(wav_file):
        print(f"文件不存在: {wav_file}")
        return
    
    try:
        # 获取文件信息
        iqs_profile, iqs_stream_info, device_info, packet_count = converter.get_iqs_wav_file_info(wav_file)
        
        print("文件信息:")
        print(f"  设备UID: {device_info.get('DeviceUID', 'N/A'):x}")
        print(f"  中心频率: {iqs_profile.get('CenterFreq_Hz', 'N/A')} Hz")
        print(f"  IQ采样率: {iqs_stream_info.get('IQSampleRate', 'N/A')}")
        print(f"  数据包数量: {packet_count}")
        print(f"  总样本数: {iqs_stream_info.get('StreamSamples', 'N/A')}")
        print(f"  每包样本数: {iqs_stream_info.get('PacketSamples', 'N/A')}")
        
    except Exception as e:
        print(f"读取文件信息失败: {e}")

def main():
    """主函数"""
    print("IQS WAV转换器使用示例")
    print("=" * 50)
    
    # 运行各个示例
    example_single_file()
    example_batch_conversion()
    example_custom_output_dir()
    example_get_file_info()
    
    print("\n" + "=" * 50)
    print("示例运行完成")
    print("请根据实际需要修改文件路径和目录路径")

if __name__ == "__main__":
    main()
